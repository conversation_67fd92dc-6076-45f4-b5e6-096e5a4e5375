# AI-FDB v0.3 - 数据表创建

## 版本概述

在工作空间管理基础上，完成数据表结构设计、字段管理、AI辅助创建功能，为数据存储和管理提供灵活的表结构定义能力。

## 功能模块

### 1. 数据表管理
- 数据表创建和配置
- 表结构设计器
- 表信息编辑和删除
- 表结构版本管理

### 2. 字段管理
- 字段类型定义
- 字段属性配置
- 字段验证规则
- 字段排序和分组

### 3. AI辅助功能
- 智能表结构生成
- 字段推荐算法
- 提示词自动生成
- 抽取规则优化

## 技术实现

### 新增技术组件
- JSON Schema 验证
- 动态表单生成
- AI服务集成框架
- 表结构版本控制

### 核心特性
- 灵活的字段类型系统
- 可视化表设计器
- 智能推荐引擎
- 实时预览功能

## 数据库设计

### 新增表结构

#### data_tables 表
```sql
CREATE TABLE data_tables (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    description TEXT,
    workspace_id BIGINT NOT NULL,
    table_schema JSON NOT NULL COMMENT '表结构定义',
    ai_config JSON COMMENT 'AI抽取配置',
    version INT DEFAULT 1,
    status ENUM('draft', 'active', 'archived') DEFAULT 'draft',
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_workspace_id (workspace_id),
    INDEX idx_name (name),
    INDEX idx_status (status),
    UNIQUE KEY uk_workspace_name (workspace_id, name)
);
```

#### table_fields 表
```sql
CREATE TABLE table_fields (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    field_name VARCHAR(100) NOT NULL,
    display_name VARCHAR(100),
    field_type ENUM('text', 'number', 'date', 'datetime', 'boolean', 'file', 'url', 'email', 'phone', 'select', 'multiselect') NOT NULL,
    is_required BOOLEAN DEFAULT FALSE,
    is_unique BOOLEAN DEFAULT FALSE,
    field_order INT DEFAULT 0,
    default_value TEXT,
    field_options JSON COMMENT '字段选项配置',
    validation_rules JSON COMMENT '字段验证规则',
    extraction_prompt TEXT COMMENT 'AI抽取提示词',
    extraction_examples JSON COMMENT '抽取示例',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    UNIQUE KEY uk_table_field (table_id, field_name),
    INDEX idx_table_id (table_id),
    INDEX idx_field_order (field_order)
);
```

#### table_templates 表
```sql
CREATE TABLE table_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50),
    description TEXT,
    template_schema JSON NOT NULL,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_category (category),
    INDEX idx_public (is_public),
    INDEX idx_usage_count (usage_count)
);
```

#### ai_generation_history 表
```sql
CREATE TABLE ai_generation_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT,
    generation_type ENUM('table_structure', 'field_suggestion', 'prompt_generation') NOT NULL,
    input_prompt TEXT NOT NULL,
    generated_result JSON NOT NULL,
    confidence_score DECIMAL(3,2),
    user_feedback ENUM('accepted', 'modified', 'rejected'),
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_generation_type (generation_type),
    INDEX idx_created_at (created_at)
);
```

## 字段类型系统

### 基础字段类型

#### 文本类型 (text)
```json
{
  "type": "text",
  "maxLength": 255,
  "minLength": 0,
  "pattern": "regex_pattern",
  "placeholder": "请输入文本"
}
```

#### 数字类型 (number)
```json
{
  "type": "number",
  "min": 0,
  "max": 999999,
  "precision": 2,
  "unit": "元"
}
```

#### 日期类型 (date/datetime)
```json
{
  "type": "date",
  "format": "YYYY-MM-DD",
  "minDate": "2020-01-01",
  "maxDate": "2030-12-31"
}
```

#### 选择类型 (select/multiselect)
```json
{
  "type": "select",
  "options": [
    {"value": "option1", "label": "选项1"},
    {"value": "option2", "label": "选项2"}
  ],
  "allowCustom": false
}
```

#### 文件类型 (file)
```json
{
  "type": "file",
  "allowedTypes": ["pdf", "doc", "docx", "jpg", "png"],
  "maxSize": 10485760,
  "multiple": false
}
```

### 验证规则系统

```json
{
  "required": true,
  "unique": false,
  "custom": [
    {
      "rule": "email",
      "message": "请输入有效的邮箱地址"
    },
    {
      "rule": "phone",
      "message": "请输入有效的手机号码"
    }
  ]
}
```

## API接口

### 数据表管理接口

#### 获取数据表列表
```
GET /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
Query Parameters:
  - page: int (默认0)
  - size: int (默认10)
  - search: string (可选)
  - status: string (可选)
```

#### 创建数据表
```
POST /api/workspaces/{workspaceId}/tables
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "displayName": "string",
  "description": "string",
  "fields": [
    {
      "fieldName": "string",
      "displayName": "string",
      "fieldType": "text|number|date|boolean|file|select",
      "isRequired": boolean,
      "fieldOptions": object,
      "validationRules": object,
      "extractionPrompt": "string"
    }
  ]
}
```

#### AI生成表结构
```
POST /api/workspaces/{workspaceId}/tables/ai-generate
Authorization: Bearer {token}
Content-Type: application/json

{
  "tableName": "string",
  "description": "string",
  "category": "string",
  "sampleData": "string" (可选)
}
```

#### 获取表结构详情
```
GET /api/workspaces/{workspaceId}/tables/{tableId}
Authorization: Bearer {token}
```

#### 更新表结构
```
PUT /api/workspaces/{workspaceId}/tables/{tableId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "displayName": "string",
  "description": "string",
  "fields": array
}
```

### 字段管理接口

#### 添加字段
```
POST /api/tables/{tableId}/fields
Authorization: Bearer {token}
Content-Type: application/json

{
  "fieldName": "string",
  "displayName": "string",
  "fieldType": "string",
  "isRequired": boolean,
  "fieldOptions": object,
  "validationRules": object,
  "extractionPrompt": "string"
}
```

#### 更新字段
```
PUT /api/tables/{tableId}/fields/{fieldId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "displayName": "string",
  "fieldOptions": object,
  "validationRules": object,
  "extractionPrompt": "string"
}
```

#### 删除字段
```
DELETE /api/tables/{tableId}/fields/{fieldId}
Authorization: Bearer {token}
```

#### 字段排序
```
PUT /api/tables/{tableId}/fields/reorder
Authorization: Bearer {token}
Content-Type: application/json

{
  "fieldOrders": [
    {"fieldId": 1, "order": 0},
    {"fieldId": 2, "order": 1}
  ]
}
```

### 模板管理接口

#### 获取表模板
```
GET /api/templates
Authorization: Bearer {token}
Query Parameters:
  - category: string (可选)
  - search: string (可选)
```

#### 从模板创建表
```
POST /api/workspaces/{workspaceId}/tables/from-template
Authorization: Bearer {token}
Content-Type: application/json

{
  "templateId": number,
  "tableName": "string",
  "customizations": object
}
```

## AI服务集成

### 表结构生成服务

```java
@Service
public class TableGenerationService {
    
    public TableGenerationResult generateTableStructure(
        String tableName, 
        String description, 
        String category) {
        
        String prompt = buildTableGenerationPrompt(tableName, description, category);
        AIResponse response = aiClient.generateCompletion(prompt);
        
        return parseTableStructure(response.getContent());
    }
    
    private String buildTableGenerationPrompt(String name, String desc, String category) {
        return String.format(
            "根据以下信息生成数据表结构：\n" +
            "表名：%s\n" +
            "描述：%s\n" +
            "类别：%s\n" +
            "请生成合适的字段列表，包括字段名、类型、是否必填、验证规则等。\n" +
            "输出格式为JSON。",
            name, desc, category
        );
    }
}
```

### 字段推荐服务

```java
@Service
public class FieldRecommendationService {
    
    public List<FieldSuggestion> suggestFields(
        String tableName, 
        String tableDescription,
        List<String> existingFields) {
        
        String prompt = buildFieldSuggestionPrompt(
            tableName, tableDescription, existingFields);
        
        AIResponse response = aiClient.generateCompletion(prompt);
        return parseFieldSuggestions(response.getContent());
    }
    
    public String generateExtractionPrompt(
        String fieldName, 
        String fieldType, 
        String context) {
        
        return String.format(
            "为字段'%s'（类型：%s）生成AI抽取提示词。\n" +
            "上下文：%s\n" +
            "提示词应该清晰、准确，便于AI理解和抽取。",
            fieldName, fieldType, context
        );
    }
}
```

## 前端界面

### 新增页面
- 数据表列表页 (`/workspaces/{id}/tables`)
- 表结构设计器 (`/workspaces/{id}/tables/create`)
- 表详情页 (`/workspaces/{id}/tables/{tableId}`)
- 字段编辑器 (`/workspaces/{id}/tables/{tableId}/fields`)
- AI助手页面 (`/workspaces/{id}/tables/ai-assistant`)

### 核心组件

#### TableDesigner - 表设计器组件
```vue
<template>
  <div class="table-designer">
    <div class="designer-header">
      <el-input v-model="tableInfo.name" placeholder="表名" />
      <el-input v-model="tableInfo.description" placeholder="表描述" />
    </div>
    
    <div class="designer-body">
      <FieldList 
        :fields="fields" 
        @add-field="handleAddField"
        @edit-field="handleEditField"
        @delete-field="handleDeleteField"
        @reorder-fields="handleReorderFields"
      />
    </div>
    
    <div class="designer-sidebar">
      <AIAssistant 
        :table-info="tableInfo"
        @suggest-fields="handleFieldSuggestions"
        @generate-prompts="handlePromptGeneration"
      />
    </div>
  </div>
</template>
```

#### FieldEditor - 字段编辑器组件
```vue
<template>
  <el-dialog v-model="visible" title="编辑字段">
    <el-form :model="fieldForm" :rules="fieldRules">
      <el-form-item label="字段名" prop="fieldName">
        <el-input v-model="fieldForm.fieldName" />
      </el-form-item>
      
      <el-form-item label="显示名" prop="displayName">
        <el-input v-model="fieldForm.displayName" />
      </el-form-item>
      
      <el-form-item label="字段类型" prop="fieldType">
        <el-select v-model="fieldForm.fieldType">
          <el-option label="文本" value="text" />
          <el-option label="数字" value="number" />
          <el-option label="日期" value="date" />
          <el-option label="布尔" value="boolean" />
          <el-option label="文件" value="file" />
          <el-option label="选择" value="select" />
        </el-select>
      </el-form-item>
      
      <FieldOptions 
        :field-type="fieldForm.fieldType"
        v-model="fieldForm.fieldOptions"
      />
      
      <ValidationRules 
        v-model="fieldForm.validationRules"
      />
      
      <el-form-item label="抽取提示词">
        <el-input 
          v-model="fieldForm.extractionPrompt"
          type="textarea"
          :rows="3"
        />
        <el-button 
          @click="generatePrompt"
          size="small"
          type="primary"
          style="margin-top: 8px"
        >
          AI生成提示词
        </el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>
```

#### AIAssistant - AI助手组件
```vue
<template>
  <div class="ai-assistant">
    <el-card header="AI助手">
      <el-button 
        @click="suggestFields"
        :loading="suggesting"
        type="primary"
        style="width: 100%; margin-bottom: 12px"
      >
        智能推荐字段
      </el-button>
      
      <el-button 
        @click="generatePrompts"
        :loading="generating"
        style="width: 100%; margin-bottom: 12px"
      >
        生成抽取提示词
      </el-button>
      
      <el-button 
        @click="optimizeStructure"
        :loading="optimizing"
        style="width: 100%"
      >
        优化表结构
      </el-button>
    </el-card>
    
    <el-card header="推荐模板" style="margin-top: 16px">
      <TemplateList 
        :templates="recommendedTemplates"
        @apply-template="handleApplyTemplate"
      />
    </el-card>
  </div>
</template>
```

## 业务流程

### 手动创建表流程
1. 用户进入表设计器
2. 输入表名和描述
3. 逐个添加字段
4. 配置字段属性和验证规则
5. 预览表结构
6. 保存表定义

### AI辅助创建流程
1. 用户输入表名和描述
2. AI分析并推荐字段结构
3. 用户确认或修改推荐结果
4. AI生成抽取提示词
5. 用户调整提示词
6. 保存完整表定义

### 字段管理流程
1. 选择要编辑的字段
2. 修改字段属性
3. 更新验证规则
4. 测试抽取提示词
5. 保存字段配置

## 测试用例

### 功能测试
- 表创建和编辑功能测试
- 字段类型和验证测试
- AI推荐功能测试
- 模板应用功能测试

### 性能测试
- 大量字段表结构加载测试
- AI服务响应时间测试
- 并发创建表测试

### 兼容性测试
- 不同浏览器兼容性测试
- 移动端响应式测试
- 字段类型兼容性测试

## 验收标准

- [ ] 用户可以创建和编辑数据表
- [ ] 支持多种字段类型和验证规则
- [ ] AI辅助功能正常工作
- [ ] 表结构设计器易用性良好
- [ ] 字段排序和管理功能完善
- [ ] 模板系统可用
- [ ] 所有API接口测试通过
- [ ] 前端界面响应式设计完成
- [ ] 性能测试满足要求
- [ ] AI服务集成稳定可靠
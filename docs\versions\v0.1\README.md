# AI-FDB v0.1 - 用户鉴权系统

## 版本概述

完成用户注册、登录、权限管理基础功能，为整个系统提供安全的用户认证基础。

## 功能模块

### 1. 用户认证
- 用户注册（邮箱/手机号验证）
- 用户登录（支持用户名/邮箱登录）
- 密码安全策略
- JWT Token认证
- 会话管理

### 2. 权限管理
- 基础角色系统（guest/user/admin）
- 登录状态维护
- Token自动刷新
- 安全退出

## 技术实现

### 后端技术栈
- Spring Boot 3.x
- Spring Security
- JWT (JSON Web Token)
- MySQL 8.0
- Redis (会话存储)
- BCrypt (密码加密)

### 前端技术栈
- Vue 3
- Element Plus
- Pinia (状态管理)
- Vue Router
- Axios (HTTP客户端)

## 数据库设计

### 核心表结构

#### users 表
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    avatar_url VARCHAR(500),
    status TINYINT DEFAULT 1,
    role ENUM('guest', 'user', 'admin') DEFAULT 'user',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

#### user_sessions 表
```sql
CREATE TABLE user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    refresh_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    device_info JSON,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## API接口

### 认证相关接口

#### 用户注册
```
POST /api/auth/register
Content-Type: application/json

{
  "username": "string",
  "email": "string",
  "password": "string",
  "phone": "string" (可选)
}
```

#### 用户登录
```
POST /api/auth/login
Content-Type: application/json

{
  "usernameOrEmail": "string",
  "password": "string",
  "rememberMe": boolean
}
```

#### Token刷新
```
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "string"
}
```

#### 用户登出
```
POST /api/auth/logout
Authorization: Bearer {token}
```

## 前端界面

### 页面结构
- 登录页面 (`/login`)
- 注册页面 (`/register`)
- 仪表板页面 (`/dashboard`) - 认证后的主页

### 组件设计
- LoginForm - 登录表单组件
- RegisterForm - 注册表单组件
- AuthGuard - 路由守卫组件
- UserMenu - 用户菜单组件

## 安全特性

### 密码安全
- 最小长度8位
- 必须包含大小写字母和数字
- BCrypt加密存储
- 登录失败次数限制

### Token安全
- JWT签名验证
- Token过期时间控制
- 刷新Token机制
- 设备信息记录

### 会话管理
- Redis存储会话信息
- 自动清理过期会话
- 并发登录控制
- IP地址验证

## 测试用例

### 单元测试
- 用户注册逻辑测试
- 密码加密验证测试
- JWT Token生成验证测试
- 权限验证测试

### 集成测试
- 注册流程端到端测试
- 登录流程端到端测试
- Token刷新流程测试
- 权限控制集成测试

### 安全测试
- SQL注入防护测试
- XSS攻击防护测试
- CSRF攻击防护测试
- 暴力破解防护测试

## 部署指南

### 环境要求
- JDK 17+
- Node.js 18+
- MySQL 8.0
- Redis 6.0+

### 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: *************************************
    username: ${DB_USERNAME}
    password: ${DB_PASSWORD}
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}

jwt:
  secret: ${JWT_SECRET}
  expiration: 86400 # 24小时
  refresh-expiration: 604800 # 7天
```

### 启动步骤
1. 创建数据库和表结构
2. 配置环境变量
3. 启动Redis服务
4. 启动后端服务
5. 构建并部署前端

## 验收标准

- [ ] 用户可以成功注册账号
- [ ] 用户可以使用用户名或邮箱登录
- [ ] JWT Token认证机制正常工作
- [ ] 登录状态可以正确维护
- [ ] Token可以自动刷新
- [ ] 用户可以安全退出
- [ ] 密码安全策略生效
- [ ] 会话管理功能正常
- [ ] 所有API接口测试通过
- [ ] 前端界面响应式设计完成
- [ ] 安全测试全部通过
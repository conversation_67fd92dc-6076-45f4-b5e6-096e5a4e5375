# AI-FDB 项目详细任务细化文档

## 项目概述

AI-FDB（AI文件数据管理系统）是一个基于AI技术的文件数据管理系统，核心目标是将非结构化电子文件转换为结构化数据，并支持自然语言查询。

## 任务 1：项目初始化与环境配置

### 1.1 创建项目目录结构

#### 实施步骤：
1. **创建根目录结构**
   ```
   AI-FDB/
   ├── backend/                 # Spring Boot后端项目
   ├── frontend/                # Vue 3前端项目
   ├── docs/                    # 项目文档
   │   ├── api/                 # API文档
   │   ├── database/            # 数据库设计文档
   │   └── deployment/          # 部署文档
   ├── scripts/                 # 部署和工具脚本
   │   ├── database/            # 数据库脚本
   │   ├── deployment/          # 部署脚本
   │   └── tools/               # 工具脚本
   ├── docker/                  # Docker配置文件
   │   ├── backend/             # 后端Docker配置
   │   ├── frontend/            # 前端Docker配置
   │   └── docker-compose.yml   # 容器编排配置
   ├── tests/                   # 集成测试
   └── README.md                # 项目说明文档
   ```

2. **创建后端项目结构**
   ```
   backend/
   ├── src/
   │   ├── main/
   │   │   ├── java/com/aifdb/
   │   │   │   ├── config/      # 配置类
   │   │   │   ├── controller/  # 控制器
   │   │   │   ├── service/     # 服务层
   │   │   │   ├── repository/  # 数据访问层
   │   │   │   ├── entity/      # 实体类
   │   │   │   ├── dto/         # 数据传输对象
   │   │   │   ├── utils/       # 工具类
   │   │   │   └── exception/   # 异常处理
   │   │   └── resources/
   │   │       ├── application.yml
   │   │       ├── application-dev.yml
   │   │       ├── application-prod.yml
   │   │       └── db/migration/
   │   └── test/
   ├── pom.xml
   └── Dockerfile
   ```

3. **创建前端项目结构**
   ```
   frontend/
   ├── src/
   │   ├── components/          # 公共组件
   │   ├── views/               # 页面组件
   │   ├── router/              # 路由配置
   │   ├── store/               # Pinia状态管理
   │   ├── api/                 # API接口
   │   ├── utils/               # 工具函数
   │   ├── assets/              # 静态资源
   │   ├── styles/              # 样式文件
   │   └── types/               # TypeScript类型定义
   ├── public/
   ├── package.json
   ├── vite.config.ts
   ├── tsconfig.json
   └── Dockerfile
   ```

#### 验收标准：
- [ ] 所有目录结构创建完成
- [ ] 每个目录包含相应的README.md说明文件
- [ ] 目录权限设置正确
- [ ] 符合团队开发规范

### 1.2 配置Git仓库

#### 实施步骤：
1. **初始化Git仓库**
   ```bash
   git init
   git config user.name "AI-FDB Team"
   git config user.email "<EMAIL>"
   ```

2. **创建.gitignore文件**
   ```gitignore
   # 编译输出
   /backend/target/
   /frontend/dist/
   /frontend/node_modules/
   
   # IDE文件
   .idea/
   .vscode/
   *.iml
   
   # 系统文件
   .DS_Store
   Thumbs.db
   
   # 日志文件
   *.log
   logs/
   
   # 配置文件
   /backend/src/main/resources/application-local.yml
   .env.local
   .env.*.local
   
   # 临时文件
   *.tmp
   *.temp
   
   # 数据库文件
   *.db
   *.sqlite
   ```

3. **配置提交规范**
   - 创建`.gitmessage`模板文件
   - 配置commit-msg钩子
   - 设置提交消息格式：`type(scope): description`
   - 类型包括：feat, fix, docs, style, refactor, test, chore

4. **设置分支策略**
   - main分支：生产环境代码
   - develop分支：开发环境代码
   - feature/*分支：功能开发分支
   - hotfix/*分支：紧急修复分支

#### 验收标准：
- [ ] Git仓库初始化完成
- [ ] .gitignore文件配置正确
- [ ] 提交规范配置完成
- [ ] 分支策略文档编写完成
- [ ] 首次提交成功

### 1.3 配置开发环境

#### 实施步骤：
1. **Java开发环境**
   - 安装JDK 17或更高版本
   - 配置JAVA_HOME环境变量
   - 安装Maven 3.8+
   - 配置Maven国内镜像源

2. **Node.js开发环境**
   - 安装Node.js 18+
   - 配置npm国内镜像源：`npm config set registry https://registry.npmmirror.com`
   - 安装pnpm：`npm install -g pnpm`

3. **数据库环境**
   - 安装MySQL 8.0
   - 安装Redis 6.0+
   - 安装MongoDB 5.0+
   - 安装Elasticsearch 8.0+
   - 配置数据库连接和权限

4. **开发工具**
   - IDE：IntelliJ IDEA / VS Code
   - 数据库管理工具：Navicat / DBeaver
   - API测试工具：Postman / Apifox
   - 版本控制：Git

#### 验收标准：
- [ ] 所有软件安装完成并可正常运行
- [ ] 环境变量配置正确
- [ ] 数据库连接测试成功
- [ ] 开发工具配置完成
- [ ] 环境配置文档编写完成

## 任务 2：版本 0.1 - 用户鉴权系统

### 2.1 数据库设计 v0.1

#### 实施步骤：
1. **创建数据库**
   ```sql
   CREATE DATABASE aifdb_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   CREATE DATABASE aifdb_test CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
   ```

2. **创建用户表**
   ```sql
   CREATE TABLE users (
       id BIGINT PRIMARY KEY AUTO_INCREMENT,
       username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
       email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
       phone VARCHAR(20) COMMENT '手机号',
       password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
       avatar_url VARCHAR(500) COMMENT '头像URL',
       status TINYINT DEFAULT 1 COMMENT '状态：1正常 0禁用',
       role ENUM('guest', 'user', 'admin') DEFAULT 'user' COMMENT '角色',
       email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
       phone_verified BOOLEAN DEFAULT FALSE COMMENT '手机是否验证',
       last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       INDEX idx_email (email),
       INDEX idx_phone (phone),
       INDEX idx_username (username),
       INDEX idx_status (status)
   ) COMMENT='用户表';
   ```

3. **创建用户会话表**
   ```sql
   CREATE TABLE user_sessions (
       id BIGINT PRIMARY KEY AUTO_INCREMENT,
       user_id BIGINT NOT NULL COMMENT '用户ID',
       session_token VARCHAR(255) UNIQUE NOT NULL COMMENT '会话令牌',
       refresh_token VARCHAR(255) UNIQUE NOT NULL COMMENT '刷新令牌',
       expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
       ip_address VARCHAR(45) COMMENT 'IP地址',
       user_agent TEXT COMMENT '用户代理',
       device_info JSON COMMENT '设备信息',
       is_active BOOLEAN DEFAULT TRUE COMMENT '是否活跃',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
       INDEX idx_token (session_token),
       INDEX idx_refresh_token (refresh_token),
       INDEX idx_user_id (user_id),
       INDEX idx_expires_at (expires_at)
   ) COMMENT='用户会话表';
   ```

4. **配置Redis连接**
   - 配置Redis连接池
   - 设置会话存储策略
   - 配置过期时间和清理策略

5. **编写数据库迁移脚本**
   - 创建Flyway迁移文件
   - 编写回滚脚本
   - 配置数据库版本管理

#### 验收标准：
- [ ] 数据库创建成功
- [ ] 所有表结构创建正确
- [ ] 索引和约束设置完成
- [ ] Redis连接配置成功
- [ ] 迁移脚本测试通过
- [ ] 数据库文档编写完成

### 2.2 后端设计 v0.1

#### 实施步骤：
1. **搭建Spring Boot项目**
   ```xml
   <!-- pom.xml 主要依赖 -->
   <dependencies>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-web</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-security</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-data-jpa</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-data-redis</artifactId>
       </dependency>
       <dependency>
           <groupId>io.jsonwebtoken</groupId>
           <artifactId>jjwt-api</artifactId>
           <version>0.11.5</version>
       </dependency>
       <dependency>
           <groupId>mysql</groupId>
           <artifactId>mysql-connector-java</artifactId>
       </dependency>
       <dependency>
           <groupId>org.springframework.boot</groupId>
           <artifactId>spring-boot-starter-validation</artifactId>
       </dependency>
   </dependencies>
   ```

2. **配置Spring Security + JWT**
   ```java
   @Configuration
   @EnableWebSecurity
   public class SecurityConfig {
       
       @Bean
       public PasswordEncoder passwordEncoder() {
           return new BCryptPasswordEncoder();
       }
       
       @Bean
       public JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint() {
           return new JwtAuthenticationEntryPoint();
       }
       
       @Bean
       public JwtRequestFilter jwtRequestFilter() {
           return new JwtRequestFilter();
       }
       
       @Bean
       public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
           http.csrf().disable()
               .authorizeHttpRequests(authz -> authz
                   .requestMatchers("/api/auth/**").permitAll()
                   .requestMatchers("/api/public/**").permitAll()
                   .anyRequest().authenticated()
               )
               .exceptionHandling().authenticationEntryPoint(jwtAuthenticationEntryPoint())
               .and()
               .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);
           
           http.addFilterBefore(jwtRequestFilter(), UsernamePasswordAuthenticationFilter.class);
           return http.build();
       }
   }
   ```

3. **实现用户注册API**
   ```java
   @RestController
   @RequestMapping("/api/auth")
   @Validated
   public class AuthController {
       
       @PostMapping("/register")
       public ResponseEntity<AuthResponse> register(@Valid @RequestBody RegisterRequest request) {
           // 验证用户名和邮箱唯一性
           // 密码加密
           // 创建用户记录
           // 发送验证邮件
           // 返回注册结果
       }
   }
   
   @Data
   @Valid
   public class RegisterRequest {
       @NotBlank(message = "用户名不能为空")
       @Size(min = 3, max = 50, message = "用户名长度必须在3-50之间")
       private String username;
       
       @NotBlank(message = "邮箱不能为空")
       @Email(message = "邮箱格式不正确")
       private String email;
       
       @NotBlank(message = "密码不能为空")
       @Size(min = 8, max = 20, message = "密码长度必须在8-20之间")
       @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,}$", 
                message = "密码必须包含大小写字母和数字")
       private String password;
       
       private String phone;
   }
   ```

4. **实现用户登录API**
   ```java
   @PostMapping("/login")
   public ResponseEntity<AuthResponse> login(@Valid @RequestBody LoginRequest request, 
                                           HttpServletRequest httpRequest) {
       // 验证用户凭据
       // 生成JWT Token
       // 创建会话记录
       // 记录登录日志
       // 返回认证信息
   }
   
   @Data
   @Valid
   public class LoginRequest {
       @NotBlank(message = "用户名或邮箱不能为空")
       private String usernameOrEmail;
       
       @NotBlank(message = "密码不能为空")
       private String password;
       
       private Boolean rememberMe = false;
   }
   ```

5. **实现Token刷新API**
   ```java
   @PostMapping("/refresh")
   public ResponseEntity<AuthResponse> refreshToken(@Valid @RequestBody RefreshTokenRequest request) {
       // 验证刷新令牌
       // 生成新的访问令牌
       // 更新会话信息
       // 返回新令牌
   }
   ```

6. **添加全局异常处理**
   ```java
   @RestControllerAdvice
   public class GlobalExceptionHandler {
       
       @ExceptionHandler(ValidationException.class)
       public ResponseEntity<ErrorResponse> handleValidation(ValidationException e) {
           // 处理参数验证异常
       }
       
       @ExceptionHandler(AuthenticationException.class)
       public ResponseEntity<ErrorResponse> handleAuthentication(AuthenticationException e) {
           // 处理认证异常
       }
       
       @ExceptionHandler(Exception.class)
       public ResponseEntity<ErrorResponse> handleGeneral(Exception e) {
           // 处理通用异常
       }
   }
   ```

#### 验收标准：
- [ ] Spring Boot项目启动成功
- [ ] JWT认证机制工作正常
- [ ] 用户注册API测试通过
- [ ] 用户登录API测试通过
- [ ] Token刷新API测试通过
- [ ] 参数验证正常工作
- [ ] 异常处理完善
- [ ] API文档生成完成

### 2.3 前端设计 v0.1

#### 实施步骤：
1. **搭建Vue 3 + Vite项目**
   ```bash
   # 创建项目
   npm create vue@latest frontend
   cd frontend
   
   # 安装依赖
   npm install
   
   # 安装UI库和工具
   npm install element-plus @element-plus/icons-vue
   npm install pinia vue-router@4
   npm install axios
   npm install @vueuse/core
   npm install sass
   ```

2. **配置Element Plus**
   ```typescript
   // main.ts
   import { createApp } from 'vue'
   import ElementPlus from 'element-plus'
   import 'element-plus/dist/index.css'
   import * as ElementPlusIconsVue from '@element-plus/icons-vue'
   import App from './App.vue'
   
   const app = createApp(App)
   
   app.use(ElementPlus)
   
   // 注册所有图标
   for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
     app.component(key, component)
   }
   
   app.mount('#app')
   ```

3. **配置Pinia状态管理**
   ```typescript
   // stores/auth.ts
   import { defineStore } from 'pinia'
   import { ref, computed } from 'vue'
   import type { User, LoginForm, RegisterForm } from '@/types/auth'
   import { authApi } from '@/api/auth'
   
   export const useAuthStore = defineStore('auth', () => {
     const user = ref<User | null>(null)
     const token = ref<string | null>(localStorage.getItem('token'))
     
     const isAuthenticated = computed(() => !!token.value && !!user.value)
     
     const login = async (form: LoginForm) => {
       try {
         const response = await authApi.login(form)
         token.value = response.data.token
         user.value = response.data.user
         localStorage.setItem('token', token.value)
         return response
       } catch (error) {
         throw error
       }
     }
     
     const register = async (form: RegisterForm) => {
       try {
         const response = await authApi.register(form)
         return response
       } catch (error) {
         throw error
       }
     }
     
     const logout = () => {
       token.value = null
       user.value = null
       localStorage.removeItem('token')
     }
     
     return {
       user,
       token,
       isAuthenticated,
       login,
       register,
       logout
     }
   })
   ```

4. **实现登录页面**
   ```vue
   <!-- views/Login.vue -->
   <template>
     <div class="login-container">
       <el-card class="login-card">
         <template #header>
           <h2>AI-FDB 登录</h2>
         </template>
         
         <el-form
           ref="loginFormRef"
           :model="loginForm"
           :rules="loginRules"
           label-width="80px"
           @submit.prevent="handleLogin"
         >
           <el-form-item label="用户名" prop="usernameOrEmail">
             <el-input
               v-model="loginForm.usernameOrEmail"
               placeholder="请输入用户名或邮箱"
               :prefix-icon="User"
             />
           </el-form-item>
           
           <el-form-item label="密码" prop="password">
             <el-input
               v-model="loginForm.password"
               type="password"
               placeholder="请输入密码"
               :prefix-icon="Lock"
               show-password
             />
           </el-form-item>
           
           <el-form-item>
             <el-checkbox v-model="loginForm.rememberMe">
               记住我
             </el-checkbox>
           </el-form-item>
           
           <el-form-item>
             <el-button
               type="primary"
               :loading="loading"
               @click="handleLogin"
               style="width: 100%"
             >
               登录
             </el-button>
           </el-form-item>
           
           <el-form-item>
             <el-link type="primary" @click="$router.push('/register')">
               还没有账号？立即注册
             </el-link>
           </el-form-item>
         </el-form>
       </el-card>
     </div>
   </template>
   
   <script setup lang="ts">
   import { ref, reactive } from 'vue'
   import { useRouter } from 'vue-router'
   import { ElMessage } from 'element-plus'
   import { User, Lock } from '@element-plus/icons-vue'
   import { useAuthStore } from '@/stores/auth'
   import type { LoginForm } from '@/types/auth'
   
   const router = useRouter()
   const authStore = useAuthStore()
   const loading = ref(false)
   const loginFormRef = ref()
   
   const loginForm = reactive<LoginForm>({
     usernameOrEmail: '',
     password: '',
     rememberMe: false
   })
   
   const loginRules = {
     usernameOrEmail: [
       { required: true, message: '请输入用户名或邮箱', trigger: 'blur' }
     ],
     password: [
       { required: true, message: '请输入密码', trigger: 'blur' },
       { min: 8, message: '密码长度不能少于8位', trigger: 'blur' }
     ]
   }
   
   const handleLogin = async () => {
     if (!loginFormRef.value) return
     
     await loginFormRef.value.validate(async (valid: boolean) => {
       if (!valid) return
       
       loading.value = true
       try {
         await authStore.login(loginForm)
         ElMessage.success('登录成功')
         router.push('/dashboard')
       } catch (error: any) {
         ElMessage.error(error.response?.data?.message || '登录失败')
       } finally {
         loading.value = false
       }
     })
   }
   </script>
   
   <style scoped>
   .login-container {
     display: flex;
     justify-content: center;
     align-items: center;
     min-height: 100vh;
     background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
   }
   
   .login-card {
     width: 400px;
     box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
   }
   </style>
   ```

5. **配置路由守卫**
   ```typescript
   // router/index.ts
   import { createRouter, createWebHistory } from 'vue-router'
   import { useAuthStore } from '@/stores/auth'
   
   const router = createRouter({
     history: createWebHistory(),
     routes: [
       {
         path: '/login',
         name: 'Login',
         component: () => import('@/views/Login.vue'),
         meta: { requiresGuest: true }
       },
       {
         path: '/register',
         name: 'Register',
         component: () => import('@/views/Register.vue'),
         meta: { requiresGuest: true }
       },
       {
         path: '/dashboard',
         name: 'Dashboard',
         component: () => import('@/views/Dashboard.vue'),
         meta: { requiresAuth: true }
       },
       {
         path: '/',
         redirect: '/dashboard'
       }
     ]
   })
   
   router.beforeEach((to, from, next) => {
     const authStore = useAuthStore()
     
     if (to.meta.requiresAuth && !authStore.isAuthenticated) {
       next('/login')
     } else if (to.meta.requiresGuest && authStore.isAuthenticated) {
       next('/dashboard')
     } else {
       next()
     }
   })
   
   export default router
   ```

6. **实现Token自动刷新**
   ```typescript
   // utils/request.ts
   import axios from 'axios'
   import { useAuthStore } from '@/stores/auth'
   import { ElMessage } from 'element-plus'
   
   const request = axios.create({
     baseURL: import.meta.env.VITE_API_BASE_URL,
     timeout: 10000
   })
   
   // 请求拦截器
   request.interceptors.request.use(
     (config) => {
       const authStore = useAuthStore()
       if (authStore.token) {
         config.headers.Authorization = `Bearer ${authStore.token}`
       }
       return config
     },
     (error) => {
       return Promise.reject(error)
     }
   )
   
   // 响应拦截器
   request.interceptors.response.use(
     (response) => {
       return response
     },
     async (error) => {
       const authStore = useAuthStore()
       
       if (error.response?.status === 401) {
         // Token过期，尝试刷新
         try {
           await authStore.refreshToken()
           // 重新发送原请求
           return request(error.config)
         } catch (refreshError) {
           // 刷新失败，跳转到登录页
           authStore.logout()
           window.location.href = '/login'
         }
       }
       
       ElMessage.error(error.response?.data?.message || '请求失败')
       return Promise.reject(error)
     }
   )
   
   export default request
   ```

#### 验收标准：
- [ ] Vue 3项目启动成功
- [ ] Element Plus集成完成
- [ ] Pinia状态管理工作正常
- [ ] 登录页面功能完整
- [ ] 注册页面功能完整
- [ ] 路由守卫正常工作
- [ ] Token自动刷新机制完成
- [ ] 响应式设计适配移动端
- [ ] 用户体验良好

## 任务 3：版本 0.2 - 工作空间管理

### 3.1 数据库设计 v0.2

#### 实施步骤：
1. **创建工作空间表**
   ```sql
   CREATE TABLE workspaces (
       id BIGINT PRIMARY KEY AUTO_INCREMENT,
       name VARCHAR(100) NOT NULL COMMENT '工作空间名称',
       description TEXT COMMENT '工作空间描述',
       owner_id BIGINT NOT NULL COMMENT '所有者ID',
       is_public BOOLEAN DEFAULT FALSE COMMENT '是否公开',
       settings JSON COMMENT '工作空间设置',
       storage_quota BIGINT DEFAULT 1073741824 COMMENT '存储配额(字节)',
       used_storage BIGINT DEFAULT 0 COMMENT '已使用存储(字节)',
       status ENUM('active', 'archived', 'deleted') DEFAULT 'active' COMMENT '状态',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
       FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
       INDEX idx_owner_id (owner_id),
       INDEX idx_status (status),
       INDEX idx_name (name)
   ) COMMENT='工作空间表';
   ```

2. **创建工作空间成员表**
   ```sql
   CREATE TABLE workspace_members (
       id BIGINT PRIMARY KEY AUTO_INCREMENT,
       workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
       user_id BIGINT NOT NULL COMMENT '用户ID',
       role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer' COMMENT '角色',
       permissions JSON COMMENT '自定义权限',
       invited_by BIGINT COMMENT '邀请人ID',
       invited_at TIMESTAMP NULL COMMENT '邀请时间',
       joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
       status ENUM('active', 'pending', 'declined', 'removed') DEFAULT 'active' COMMENT '状态',
       FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
       FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
       FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
       UNIQUE KEY uk_workspace_user (workspace_id, user_id),
       INDEX idx_workspace_id (workspace_id),
       INDEX idx_user_id (user_id),
       INDEX idx_role (role)
   ) COMMENT='工作空间成员表';
   ```

3. **创建邀请记录表**
   ```sql
   CREATE TABLE workspace_invitations (
       id BIGINT PRIMARY KEY AUTO_INCREMENT,
       workspace_id BIGINT NOT NULL COMMENT '工作空间ID',
       email VARCHAR(100) NOT NULL COMMENT '邀请邮箱',
       role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer' COMMENT '邀请角色',
       invited_by BIGINT NOT NULL COMMENT '邀请人ID',
       invitation_token VARCHAR(255) UNIQUE NOT NULL COMMENT '邀请令牌',
       expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
       status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending' COMMENT '状态',
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       accepted_at TIMESTAMP NULL COMMENT '接受时间',
       FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
       FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
       INDEX idx_workspace_id (workspace_id),
       INDEX idx_email (email),
       INDEX idx_token (invitation_token),
       INDEX idx_status (status)
   ) COMMENT='工作空间邀请表';
   ```

4. **设计权限角色体系**
   ```json
   {
     "owner": {
       "description": "工作空间所有者",
       "permissions": [
         "workspace.delete",
         "workspace.update",
         "workspace.manage_members",
         "workspace.manage_settings",
         "table.create",
         "table.update",
         "table.delete",
         "record.create",
         "record.update",
         "record.delete",
         "file.upload",
         "file.delete"
       ]
     },
     "admin": {
       "description": "管理员",
       "permissions": [
         "workspace.update",
         "workspace.manage_members",
         "table.create",
         "table.update",
         "table.delete",
         "record.create",
         "record.update",
         "record.delete",
         "file.upload",
         "file.delete"
       ]
     },
     "editor": {
       "description": "编辑者",
       "permissions": [
         "table.create",
         "table.update",
         "record.create",
         "record.update",
         "record.delete",
         "file.upload"
       ]
     },
     "viewer": {
       "description": "查看者",
       "permissions": [
         "workspace.view",
         "table.view",
         "record.view",
         "file.view"
       ]
     }
   }
   ```

#### 验收标准：
- [ ] 工作空间相关表创建完成
- [ ] 权限角色体系设计完成
- [ ] 数据库约束和索引设置正确
- [ ] 迁移脚本编写完成
- [ ] 权限验证逻辑设计完成

### 3.2 后端设计 v0.2

#### 实施步骤：
1. **创建工作空间实体类**
   ```java
   @Entity
   @Table(name = "workspaces")
   @Data
   @NoArgsConstructor
   @AllArgsConstructor
   public class Workspace {
       @Id
       @GeneratedValue(strategy = GenerationType.IDENTITY)
       private Long id;
       
       @Column(nullable = false, length = 100)
       private String name;
       
       @Column(columnDefinition = "TEXT")
       private String description;
       
       @Column(name = "owner_id", nullable = false)
       private Long ownerId;
       
       @Column(name = "is_public")
       private Boolean isPublic = false;
       
       @Column(columnDefinition = "JSON")
       private String settings;
       
       @Column(name = "storage_quota")
       private Long storageQuota = 1073741824L; // 1GB
       
       @Column(name = "used_storage")
       private Long usedStorage = 0L;
       
       @Enumerated(EnumType.STRING)
       private WorkspaceStatus status = WorkspaceStatus.ACTIVE;
       
       @CreationTimestamp
       @Column(name = "created_at")
       private LocalDateTime createdAt;
       
       @UpdateTimestamp
       @Column(name = "updated_at")
       private LocalDateTime updatedAt;
       
       // 关联关系
       @ManyToOne(fetch = FetchType.LAZY)
       @JoinColumn(name = "owner_id", insertable = false, updatable = false)
       private User owner;
       
       @OneToMany(mappedBy = "workspace", cascade = CascadeType.ALL)
       private List<WorkspaceMember> members = new ArrayList<>();
   }
   
   public enum WorkspaceStatus {
       ACTIVE, ARCHIVED, DELETED
   }
   ```

2. **实现权限验证中间件**
   ```java
   @Component
   public class WorkspacePermissionEvaluator {
       
       @Autowired
       private WorkspaceMemberRepository memberRepository;
       
       public boolean hasPermission(Long userId, Long workspaceId, String permission) {
           WorkspaceMember member = memberRepository
               .findByWorkspaceIdAndUserId(workspaceId, userId)
               .orElse(null);
           
           if (member == null) {
               return false;
           }
           
           return hasRolePermission(member.getRole(), permission) ||
                  hasCustomPermission(member.getPermissions(), permission);
       }
       
       private boolean hasRolePermission(MemberRole role, String permission) {
           Set<String> rolePermissions = getRolePermissions(role);
           return rolePermissions.contains(permission);
       }
       
       private Set<String> getRolePermissions(MemberRole role) {
           switch (role) {
               case OWNER:
                   return Set.of(
                       "workspace.delete", "workspace.update", "workspace.manage_members",
                       "workspace.manage_settings", "table.create", "table.update",
                       "table.delete", "record.create", "record.update", "record.delete",
                       "file.upload", "file.delete"
                   );
               case ADMIN:
                   return Set.of(
                       "workspace.update", "workspace.manage_members", "table.create",
                       "table.update", "table.delete", "record.create", "record.update",
                       "record.delete", "file.upload", "file.delete"
                   );
               case EDITOR:
                   return Set.of(
                       "table.create", "table.update", "record.create",
                       "record.update", "record.delete", "file.upload"
                   );
               case VIEWER:
                   return Set.of(
                       "workspace.view", "table.view", "record.view", "file.view"
                   );
               default:
                   return Set.of();
           }
       }
   }
   
   @Target({ElementType.METHOD, ElementType.TYPE})
   @Retention(RetentionPolicy.RUNTIME)
   public @interface RequireWorkspacePermission {
       String value();
   }
   
   @Aspect
   @Component
   public class WorkspacePermissionAspect {
       
       @Autowired
       private WorkspacePermissionEvaluator permissionEvaluator;
       
       @Around("@annotation(requirePermission)")
       public Object checkPermission(ProceedingJoinPoint joinPoint, 
                                   RequireWorkspacePermission requirePermission) throws Throwable {
           
           // 获取当前用户ID和工作空间ID
           Long userId = getCurrentUserId();
           Long workspaceId = getWorkspaceIdFromArgs(joinPoint.getArgs());
           
           if (!permissionEvaluator.hasPermission(userId, workspaceId, requirePermission.value())) {
               throw new AccessDeniedException("没有权限执行此操作");
           }
           
           return joinPoint.proceed();
       }
   }
   ```

3. **实现工作空间CRUD API**
   ```java
   @RestController
   @RequestMapping("/api/workspaces")
   @Validated
   public class WorkspaceController {
       
       @Autowired
       private WorkspaceService workspaceService;
       
       @GetMapping
       public ResponseEntity<PageResponse<WorkspaceDTO>> getWorkspaces(
           @RequestParam(defaultValue = "0") int page,
           @RequestParam(defaultValue = "10") int size,
           @RequestParam(required = false) String search,
           @RequestParam(required = false) WorkspaceStatus status) {
           
           Long userId = getCurrentUserId();
           PageRequest pageRequest = PageRequest.of(page, size);
           Page<WorkspaceDTO> workspaces = workspaceService.getUserWorkspaces(
               userId, search, status, pageRequest);
           
           return ResponseEntity.ok(PageResponse.of(workspaces));
       }
       
       @PostMapping
       public ResponseEntity<WorkspaceDTO> createWorkspace(
           @Valid @RequestBody CreateWorkspaceRequest request) {
           
           Long userId = getCurrentUserId();
           WorkspaceDTO workspace = workspaceService.createWorkspace(userId, request);
           
           return ResponseEntity.status(HttpStatus.CREATED).body(workspace);
       }
       
       @GetMapping("/{id}")
       @RequireWorkspacePermission("workspace.view")
       public ResponseEntity<WorkspaceDetailDTO> getWorkspace(@PathVariable Long id) {
           WorkspaceDetailDTO workspace = workspaceService.getWorkspaceDetail(id);
           return ResponseEntity.ok(workspace);
       }
       
       @PutMapping("/{id}")
       @RequireWorkspacePermission("workspace.update")
       public ResponseEntity<WorkspaceDTO> updateWorkspace(
           @PathVariable Long id,
           @Valid @RequestBody UpdateWorkspaceRequest request) {
           
           WorkspaceDTO workspace = workspaceService.updateWorkspace(id, request);
           return ResponseEntity.ok(workspace);
       }
       
       @DeleteMapping("/{id}")
       @RequireWorkspacePermission("workspace.delete")
       public ResponseEntity<Void> deleteWorkspace(@PathVariable Long id) {
           workspaceService.deleteWorkspace(id);
           return ResponseEntity.noContent().build();
       }
   }
   ```

4. **实现成员管理功能**
   ```java
   @RestController
   @RequestMapping("/api/workspaces/{workspaceId}/members")
   public class WorkspaceMemberController {
       
       @Autowired
       private WorkspaceMemberService memberService;
       
       @GetMapping
       @RequireWorkspacePermission("workspace.view")
       public ResponseEntity<List<WorkspaceMemberDTO>> getMembers(
           @PathVariable Long workspaceId) {
           
           List<WorkspaceMemberDTO> members = memberService.getWorkspaceMembers(workspaceId);
           return ResponseEntity.ok(members);
       }
       
       @PostMapping("/invite")
       @RequireWorkspacePermission("workspace.manage_members")
       public ResponseEntity<WorkspaceInvitationDTO> inviteMember(
           @PathVariable Long workspaceId,
           @Valid @RequestBody InviteMemberRequest request) {
           
           Long inviterId = getCurrentUserId();
           WorkspaceInvitationDTO invitation = memberService.inviteMember(
               workspaceId, inviterId, request);
           
           return ResponseEntity.status(HttpStatus.CREATED).body(invitation);
       }
       
       @PutMapping("/{memberId}/role")
       @RequireWorkspacePermission("workspace.manage_members")
       public ResponseEntity<WorkspaceMemberDTO> updateMemberRole(
           @PathVariable Long workspaceId,
           @PathVariable Long memberId,
           @Valid @RequestBody UpdateMemberRoleRequest request) {
           
           WorkspaceMemberDTO member = memberService.updateMemberRole(
               workspaceId, memberId, request.getRole());
           
           return ResponseEntity.ok(member);
       }
       
       @DeleteMapping("/{memberId}")
       @RequireWorkspacePermission("workspace.manage_members")
       public ResponseEntity<Void> removeMember(
           @PathVariable Long workspaceId,
           @PathVariable Long memberId) {
           
           memberService.removeMember(workspaceId, memberId);
           return ResponseEntity.noContent().build();
       }
   }
   ```

#### 验收标准：
- [ ] 工作空间CRUD API实现完成
- [ ] 权限验证中间件工作正常
- [ ] 成员管理功能完整
- [ ] 邀请机制实现完成
- [ ] API文档更新完成
- [ ] 单元测试覆盖率达到80%

### 3.3 前端设计 v0.2

#### 实施步骤：
1. **实现工作空间列表页面**
   ```vue
   <!-- views/WorkspaceList.vue -->
   <template>
     <div class="workspace-list">
       <div class="header">
         <h1>我的工作空间</h1>
         <el-button type="primary" @click="showCreateDialog = true">
           <el-icon><Plus /></el-icon>
           创建工作空间
         </el-button>
       </div>
       
       <div class="filters">
         <el-input
           v-model="searchQuery"
           placeholder="搜索工作空间"
           :prefix-icon="Search"
           style="width: 300px"
           @input="handleSearch"
         />
         
         <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
           <el-option label="全部" value="" />
           <el-option label="活跃" value="active" />
           <el-option label="已归档" value="archived" />
         </el-select>
         
## 文档结构说明

本项目的详细任务文档已分解为以下多个独立文档，便于管理和查阅：

### 主要文档列表：

1. **项目初始化文档** - `e:\AI-FDB\docs\01-project-initialization.md`
   - 项目目录结构创建
   - Git仓库配置
   - 开发环境配置

2. **数据库设计文档** - `e:\AI-FDB\docs\02-database-design.md`
   - 数据库架构设计
   - 表结构定义
   - 索引和约束设计

3. **后端开发文档** - `e:\AI-FDB\docs\03-backend-development.md`
   - Spring Boot项目搭建
   - API接口设计
   - 服务层实现

4. **前端开发文档** - `e:\AI-FDB\docs\04-frontend-development.md`
   - Vue 3项目搭建
   - 组件开发
   - 状态管理

5. **版本开发计划** - `e:\AI-FDB\docs\05-version-development-plan.md`
   - 各版本详细开发计划
   - 功能模块分解
   - 开发里程碑

6. **测试与部署文档** - `e:\AI-FDB\docs\06-testing-deployment.md`
   - 测试策略
   - 部署方案
   - 运维监控

### 任务执行顺序：

**阶段一：基础环境搭建**
- 参考文档：`01-project-initialization.md`
- 完成项目初始化、Git配置、开发环境搭建

**阶段二：数据库设计**
- 参考文档：`02-database-design.md`
- 完成数据库设计、表结构创建、数据迁移

**阶段三：后端开发**
- 参考文档：`03-backend-development.md`
- 完成Spring Boot项目、API接口、业务逻辑

**阶段四：前端开发**
- 参考文档：`04-frontend-development.md`
- 完成Vue 3项目、页面组件、用户交互

**阶段五：版本迭代**
- 参考文档：`05-version-development-plan.md`
- 按版本计划逐步实现功能模块

**阶段六：测试部署**
- 参考文档：`06-testing-deployment.md`
- 完成测试、部署、上线运维

---

## 任务 1：项目初始化与环境配置

> **详细文档位置**：`e:\AI-FDB\docs\01-project-initialization.md`

### 概述
本任务负责搭建AI-FDB项目的基础开发环境，包括目录结构创建、Git仓库配置和开发工具安装。

### 主要子任务：
1. **创建项目目录结构** - 建立标准化的项目文件组织
2. **配置Git仓库** - 设置版本控制和协作规范
3. **配置开发环境** - 安装必要的开发工具和依赖

### 验收标准：
- [ ] 项目目录结构完整
- [ ] Git仓库配置正确
- [ ] 开发环境可正常运行
- [ ] 环境配置文档完成

---

## 任务 2：版本 0.1 - 用户鉴权系统

> **详细文档位置**：
> - 数据库设计：`e:\AI-FDB\docs\02-database-design.md` (第2.1节)
> - 后端设计：`e:\AI-FDB\docs\03-backend-development.md` (第2.1节)
> - 前端设计：`e:\AI-FDB\docs\04-frontend-development.md` (第2.1节)

### 概述
实现用户注册、登录、JWT认证等基础用户管理功能。

### 主要子任务：
1. **数据库设计 v0.1** - 用户表、会话表设计
2. **后端设计 v0.1** - Spring Security + JWT认证
3. **前端设计 v0.1** - 登录注册页面、路由守卫

### 验收标准：
- [ ] 用户注册登录功能正常
- [ ] JWT认证机制工作
- [ ] 前端路由守卫生效
- [ ] API文档完成

---

## 任务 3：版本 0.2 - 工作空间管理

> **详细文档位置**：
> - 数据库设计：`e:\AI-FDB\docs\02-database-design.md` (第3.1节)
> - 后端设计：`e:\AI-FDB\docs\03-backend-development.md` (第3.1节)
> - 前端设计：`e:\AI-FDB\docs\04-frontend-development.md` (第3.1节)

### 概述
实现工作空间的创建、管理、成员邀请等功能。

### 主要子任务：
1. **数据库设计 v0.2** - 工作空间表、成员表、权限设计
2. **后端设计 v0.2** - 工作空间CRUD、权限验证
3. **前端设计 v0.2** - 工作空间列表、创建表单、成员管理

### 验收标准：
- [ ] 工作空间CRUD功能完整
- [ ] 权限验证机制正常
- [ ] 成员邀请功能工作
- [ ] 前端界面友好

---

## 任务 4：版本 0.3 - 数据表创建

> **详细文档位置**：
> - 数据库设计：`e:\AI-FDB\docs\02-database-design.md` (第4.1节)
> - 后端设计：`e:\AI-FDB\docs\03-backend-development.md` (第4.1节)
> - 前端设计：`e:\AI-FDB\docs\04-frontend-development.md` (第4.1节)

### 概述
实现动态数据表创建、字段设计、AI辅助配置等功能。

### 主要子任务：
1. **数据库设计 v0.3** - 数据表定义、字段定义表
2. **后端设计 v0.3** - 动态表创建、AI字段推荐
3. **前端设计 v0.3** - 表设计器、字段配置界面

### 验收标准：
- [ ] 动态表创建功能正常
- [ ] AI字段推荐工作
- [ ] 表设计器易用性良好
- [ ] 字段验证规则生效

---

## 任务 5：版本 0.4 - 数据记录录入

> **详细文档位置**：
> - 数据库设计：`e:\AI-FDB\docs\02-database-design.md` (第5.1节)
> - 后端设计：`e:\AI-FDB\docs\03-backend-development.md` (第5.1节)
> - 前端设计：`e:\AI-FDB\docs\04-frontend-development.md` (第5.1节)

### 概述
实现数据记录的手动录入、AI抽取、批量处理等功能。

### 主要子任务：
1. **数据库设计 v0.4** - 数据记录表、文件存储表
2. **后端设计 v0.4** - 记录CRUD、AI抽取服务
3. **前端设计 v0.4** - 记录表单、文件上传、批量处理

### 验收标准：
- [ ] 手动录入功能完整
- [ ] AI抽取功能正常
- [ ] 批量处理性能良好
- [ ] 文件上传稳定

---

## 任务 6：版本 0.5 - 示例数据表

> **详细文档位置**：`e:\AI-FDB\docs\05-version-development-plan.md` (第6节)

### 概述
创建示例数据表，提供数据中心展示功能。

### 主要子任务：
1. **示例数据准备** - 创建各行业示例数据
2. **数据中心页面** - 展示和引用功能
3. **引用机制** - 示例表复制到工作空间

### 验收标准：
- [ ] 示例数据丰富完整
- [ ] 数据中心界面美观
- [ ] 引用功能正常工作
- [ ] 数据脱敏处理正确

---

## 任务 7：版本 1.0 - 完整可运行系统

> **详细文档位置**：`e:\AI-FDB\docs\05-version-development-plan.md` (第7节)

### 概述
集成所有功能模块，完善系统性能和用户体验。

### 主要子任务：
1. **系统集成** - 模块整合和优化
2. **性能优化** - 数据库查询、前端渲染优化
3. **用户体验** - 界面美化、交互优化
4. **系统监控** - 日志、监控、告警

### 验收标准：
- [ ] 所有功能模块正常工作
- [ ] 系统性能满足要求
- [ ] 用户体验良好
- [ ] 监控告警完善

---

## 任务 8：AI服务集成

> **详细文档位置**：`e:\AI-FDB\docs\03-backend-development.md` (第8节)

### 概述
集成多种AI服务，实现智能数据抽取和自然语言查询。

### 主要子任务：
1. **AI服务接入** - OpenAI、百度文心、阿里通义
2. **数据抽取引擎** - 文档解析、信息提取
3. **自然语言查询** - 语义理解、SQL生成
4. **AI任务队列** - 异步处理、状态管理

### 验收标准：
- [ ] 多AI服务正常工作
- [ ] 数据抽取准确率高
- [ ] 自然语言查询响应快
- [ ] 任务队列稳定可靠

---

## 任务 9：系统部署与运维

> **详细文档位置**：`e:\AI-FDB\docs\06-testing-deployment.md`

### 概述
完成系统的部署、监控、备份等运维工作。

### 主要子任务：
1. **Docker容器化** - 应用打包、镜像构建
2. **部署方案** - 生产环境部署
3. **监控告警** - 系统监控、性能监控
4. **备份恢复** - 数据备份、灾难恢复

### 验收标准：
- [ ] Docker部署成功
- [ ] 生产环境稳定运行
- [ ] 监控告警及时
- [ ] 备份恢复可靠

---

## 任务 10：测试与质量保证

> **详细文档位置**：`e:\AI-FDB\docs\06-testing-deployment.md` (第10节)

### 概述
建立完整的测试体系，确保系统质量。

### 主要子任务：
1. **单元测试** - 后端服务单元测试
2. **集成测试** - API接口集成测试
3. **前端测试** - 组件测试、E2E测试
4. **性能测试** - 压力测试、负载测试

### 验收标准：
- [ ] 单元测试覆盖率>80%
- [ ] 集成测试通过率100%
- [ ] 前端测试覆盖主要功能
- [ ] 性能测试满足要求

---

## 开发里程碑

### 阶段一：基础架构 (v0.1-0.2)
- **时间**：4-6周
- **目标**：完成用户认证和工作空间管理
- **交付物**：可登录的基础系统

### 阶段二：核心功能 (v0.3-0.4)
- **时间**：6-8周
- **目标**：完成数据表创建和记录录入
- **交付物**：可用的数据管理系统

### 阶段三：完善体验 (v0.5-1.0)
- **时间**：4-6周
- **目标**：完善功能和用户体验
- **交付物**：生产就绪的完整系统

---

## 技术栈总结

### 后端技术栈
- **框架**：Spring Boot 3.x
- **数据库**：MySQL 8.0, MongoDB, Redis, Elasticsearch
- **消息队列**：RabbitMQ
- **AI服务**：OpenAI API, 百度文心一言, 阿里通义千问

### 前端技术栈
- **框架**：Vue 3 + TypeScript
- **UI库**：Element Plus
- **状态管理**：Pinia
- **路由**：Vue Router 4
- **构建工具**：Vite

### 开发工具
- **版本控制**：Git
- **API文档**：Swagger/OpenAPI
- **测试框架**：Jest, Playwright
- **容器化**：Docker
- **监控**：Prometheus + Grafana
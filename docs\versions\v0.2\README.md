# AI-FDB v0.2 - 工作空间管理

## 版本概述

在用户认证系统基础上，完成工作空间创建、管理、权限控制功能，为用户提供数据组织和协作的基础平台。

## 功能模块

### 1. 工作空间管理
- 工作空间创建和配置
- 工作空间列表展示
- 工作空间编辑和删除
- 工作空间权限设置

### 2. 成员管理
- 成员邀请机制
- 角色权限分配
- 成员列表管理
- 权限变更和移除

### 3. 权限控制
- 四级权限体系（owner/admin/editor/viewer）
- 基于角色的访问控制
- 操作权限验证
- 数据访问隔离

## 技术实现

### 新增技术组件
- Spring Security 权限扩展
- 多租户数据隔离
- 邮件服务集成
- 权限注解框架

### 架构扩展
- 工作空间上下文管理
- 权限缓存机制
- 审计日志记录
- 事件驱动通知

## 数据库设计

### 新增表结构

#### workspaces 表
```sql
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name)
);
```

#### workspace_members 表
```sql
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP NULL,
    status ENUM('pending', 'active', 'inactive') DEFAULT 'pending',
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);
```

#### workspace_invitations 表
```sql
CREATE TABLE workspace_invitations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (invitation_token),
    INDEX idx_email (email),
    INDEX idx_status (status)
);
```

## API接口

### 工作空间管理接口

#### 获取工作空间列表
```
GET /api/workspaces
Authorization: Bearer {token}
Query Parameters:
  - page: int (默认0)
  - size: int (默认10)
  - search: string (可选)
```

#### 创建工作空间
```
POST /api/workspaces
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "isPublic": boolean,
  "settings": {
    "allowInvitations": boolean,
    "defaultRole": "viewer|editor|admin"
  }
}
```

#### 获取工作空间详情
```
GET /api/workspaces/{id}
Authorization: Bearer {token}
```

#### 更新工作空间
```
PUT /api/workspaces/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "isPublic": boolean,
  "settings": object
}
```

#### 删除工作空间
```
DELETE /api/workspaces/{id}
Authorization: Bearer {token}
```

### 成员管理接口

#### 获取成员列表
```
GET /api/workspaces/{id}/members
Authorization: Bearer {token}
```

#### 邀请成员
```
POST /api/workspaces/{id}/members/invite
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "string",
  "role": "admin|editor|viewer",
  "message": "string" (可选)
}
```

#### 更新成员角色
```
PUT /api/workspaces/{id}/members/{userId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "role": "admin|editor|viewer"
}
```

#### 移除成员
```
DELETE /api/workspaces/{id}/members/{userId}
Authorization: Bearer {token}
```

#### 接受邀请
```
POST /api/invitations/{token}/accept
Authorization: Bearer {token}
```

## 权限体系

### 角色定义

#### Owner (所有者)
- 工作空间的创建者
- 拥有所有权限
- 可以删除工作空间
- 可以转让所有权

#### Admin (管理员)
- 可以管理成员和权限
- 可以修改工作空间设置
- 可以创建和管理数据表
- 不能删除工作空间

#### Editor (编辑者)
- 可以创建和编辑数据表
- 可以添加和修改记录
- 可以查看所有数据
- 不能管理成员

#### Viewer (查看者)
- 只能查看数据
- 不能进行任何修改操作
- 不能邀请其他成员

### 权限矩阵

| 操作 | Owner | Admin | Editor | Viewer |
|------|-------|-------|--------|---------|
| 查看工作空间 | ✓ | ✓ | ✓ | ✓ |
| 编辑工作空间 | ✓ | ✓ | ✗ | ✗ |
| 删除工作空间 | ✓ | ✗ | ✗ | ✗ |
| 邀请成员 | ✓ | ✓ | ✗ | ✗ |
| 管理成员 | ✓ | ✓ | ✗ | ✗ |
| 创建数据表 | ✓ | ✓ | ✓ | ✗ |
| 编辑数据表 | ✓ | ✓ | ✓ | ✗ |
| 查看数据 | ✓ | ✓ | ✓ | ✓ |
| 编辑数据 | ✓ | ✓ | ✓ | ✗ |

## 前端界面

### 新增页面
- 工作空间列表页 (`/workspaces`)
- 工作空间详情页 (`/workspaces/{id}`)
- 工作空间设置页 (`/workspaces/{id}/settings`)
- 成员管理页 (`/workspaces/{id}/members`)
- 邀请接受页 (`/invitations/{token}`)

### 新增组件
- WorkspaceCard - 工作空间卡片组件
- WorkspaceForm - 工作空间表单组件
- MemberList - 成员列表组件
- InviteMember - 邀请成员组件
- RoleSelector - 角色选择组件
- PermissionGuard - 权限守卫组件

### 界面特性
- 响应式网格布局
- 实时权限控制
- 操作确认对话框
- 加载状态指示
- 错误处理提示

## 业务流程

### 工作空间创建流程
1. 用户点击"创建工作空间"
2. 填写工作空间信息
3. 设置初始权限配置
4. 系统创建工作空间记录
5. 用户自动成为Owner
6. 跳转到工作空间详情页

### 成员邀请流程
1. 管理员输入邀请邮箱和角色
2. 系统生成邀请令牌
3. 发送邀请邮件
4. 被邀请人点击邮件链接
5. 登录或注册账号
6. 确认接受邀请
7. 加入工作空间

### 权限变更流程
1. 管理员选择成员
2. 修改成员角色
3. 系统验证操作权限
4. 更新权限记录
5. 发送通知邮件
6. 实时更新界面权限

## 安全特性

### 数据隔离
- 基于工作空间的数据隔离
- 跨工作空间访问控制
- 敏感操作审计日志

### 权限验证
- 接口级权限验证
- 数据级权限过滤
- 前端权限控制

### 邀请安全
- 邀请令牌时效控制
- 邮箱验证机制
- 防止恶意邀请

## 测试用例

### 功能测试
- 工作空间CRUD操作测试
- 成员邀请流程测试
- 权限控制验证测试
- 邮件发送功能测试

### 权限测试
- 不同角色权限边界测试
- 跨工作空间访问控制测试
- 权限升级降级测试

### 安全测试
- 权限绕过攻击测试
- 邀请令牌安全测试
- 数据泄露防护测试

## 部署配置

### 邮件服务配置
```yaml
spring:
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
```

### 权限缓存配置
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false
```

## 验收标准

- [ ] 用户可以创建和管理工作空间
- [ ] 工作空间权限控制正常工作
- [ ] 成员邀请流程完整可用
- [ ] 四级权限体系正确实现
- [ ] 邮件通知功能正常
- [ ] 数据隔离机制有效
- [ ] 所有API接口测试通过
- [ ] 前端权限控制完善
- [ ] 安全测试全部通过
- [ ] 性能测试满足要求
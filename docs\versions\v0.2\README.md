# AI-FDB v0.2 - 工作空间管理

## 版本概述

v0.2版本在v0.1用户认证系统基础上，完整实现工作空间管理功能。用户登录后可以创建工作空间、邀请成员、管理权限，为多用户协作提供基础平台。本版本可以完整运行并进行可视化验证。

## 🎯 可视化验证目标

完成v0.2版本后，用户可以：
1. **工作空间管理** - 创建、编辑、删除工作空间
2. **成员邀请** - 通过邮箱邀请其他用户加入工作空间
3. **权限管理** - 设置成员角色（Owner/Admin/Editor/Viewer）
4. **工作空间切换** - 在多个工作空间之间切换
5. **成员管理** - 查看成员列表，修改成员权限
6. **邀请处理** - 接受或拒绝工作空间邀请

## 📋 完整实现清单

### 数据库扩展
- [x] 创建工作空间表结构
- [x] 创建成员关系表
- [x] 创建邀请表结构
- [x] 更新用户表支持多工作空间

### 后端功能实现
- [x] 工作空间CRUD API
- [x] 成员管理API
- [x] 邀请系统API
- [x] 权限验证中间件
- [x] 邮件服务集成

### 前端功能实现
- [x] 工作空间列表页面
- [x] 工作空间创建/编辑表单
- [x] 成员管理界面
- [x] 邀请发送界面
- [x] 权限控制组件

### 权限系统
- [x] 四级权限体系
- [x] 基于角色的访问控制
- [x] 数据隔离机制
- [x] 操作权限验证

## 功能模块

### 1. 工作空间管理
- 工作空间创建和配置
- 工作空间列表展示
- 工作空间编辑和删除
- 工作空间权限设置

### 2. 成员管理
- 成员邀请机制
- 角色权限分配
- 成员列表管理
- 权限变更和移除

### 3. 权限控制
- 四级权限体系（owner/admin/editor/viewer）
- 基于角色的访问控制
- 操作权限验证
- 数据访问隔离

## 技术实现

### 新增技术组件
- Spring Security 权限扩展
- 多租户数据隔离
- 邮件服务集成
- 权限注解框架

### 架构扩展
- 工作空间上下文管理
- 权限缓存机制
- 审计日志记录
- 事件驱动通知

## 数据库设计

### 新增表结构

#### workspaces 表
```sql
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name)
);
```

#### workspace_members 表
```sql
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP NULL,
    status ENUM('pending', 'active', 'inactive') DEFAULT 'pending',
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);
```

#### workspace_invitations 表
```sql
CREATE TABLE workspace_invitations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (invitation_token),
    INDEX idx_email (email),
    INDEX idx_status (status)
);
```

## API接口

### 工作空间管理接口

#### 获取工作空间列表
```
GET /api/workspaces
Authorization: Bearer {token}
Query Parameters:
  - page: int (默认0)
  - size: int (默认10)
  - search: string (可选)
```

#### 创建工作空间
```
POST /api/workspaces
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "isPublic": boolean,
  "settings": {
    "allowInvitations": boolean,
    "defaultRole": "viewer|editor|admin"
  }
}
```

#### 获取工作空间详情
```
GET /api/workspaces/{id}
Authorization: Bearer {token}
```

#### 更新工作空间
```
PUT /api/workspaces/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "string",
  "description": "string",
  "isPublic": boolean,
  "settings": object
}
```

#### 删除工作空间
```
DELETE /api/workspaces/{id}
Authorization: Bearer {token}
```

### 成员管理接口

#### 获取成员列表
```
GET /api/workspaces/{id}/members
Authorization: Bearer {token}
```

#### 邀请成员
```
POST /api/workspaces/{id}/members/invite
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "string",
  "role": "admin|editor|viewer",
  "message": "string" (可选)
}
```

#### 更新成员角色
```
PUT /api/workspaces/{id}/members/{userId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "role": "admin|editor|viewer"
}
```

#### 移除成员
```
DELETE /api/workspaces/{id}/members/{userId}
Authorization: Bearer {token}
```

#### 接受邀请
```
POST /api/invitations/{token}/accept
Authorization: Bearer {token}
```

## 权限体系

### 角色定义

#### Owner (所有者)
- 工作空间的创建者
- 拥有所有权限
- 可以删除工作空间
- 可以转让所有权

#### Admin (管理员)
- 可以管理成员和权限
- 可以修改工作空间设置
- 可以创建和管理数据表
- 不能删除工作空间

#### Editor (编辑者)
- 可以创建和编辑数据表
- 可以添加和修改记录
- 可以查看所有数据
- 不能管理成员

#### Viewer (查看者)
- 只能查看数据
- 不能进行任何修改操作
- 不能邀请其他成员

### 权限矩阵

| 操作 | Owner | Admin | Editor | Viewer |
|------|-------|-------|--------|---------|
| 查看工作空间 | ✓ | ✓ | ✓ | ✓ |
| 编辑工作空间 | ✓ | ✓ | ✗ | ✗ |
| 删除工作空间 | ✓ | ✗ | ✗ | ✗ |
| 邀请成员 | ✓ | ✓ | ✗ | ✗ |
| 管理成员 | ✓ | ✓ | ✗ | ✗ |
| 创建数据表 | ✓ | ✓ | ✓ | ✗ |
| 编辑数据表 | ✓ | ✓ | ✓ | ✗ |
| 查看数据 | ✓ | ✓ | ✓ | ✓ |
| 编辑数据 | ✓ | ✓ | ✓ | ✗ |

## 前端界面

### 新增页面
- 工作空间列表页 (`/workspaces`)
- 工作空间详情页 (`/workspaces/{id}`)
- 工作空间设置页 (`/workspaces/{id}/settings`)
- 成员管理页 (`/workspaces/{id}/members`)
- 邀请接受页 (`/invitations/{token}`)

### 新增组件
- WorkspaceCard - 工作空间卡片组件
- WorkspaceForm - 工作空间表单组件
- MemberList - 成员列表组件
- InviteMember - 邀请成员组件
- RoleSelector - 角色选择组件
- PermissionGuard - 权限守卫组件

### 界面特性
- 响应式网格布局
- 实时权限控制
- 操作确认对话框
- 加载状态指示
- 错误处理提示

## 业务流程

### 工作空间创建流程
1. 用户点击"创建工作空间"
2. 填写工作空间信息
3. 设置初始权限配置
4. 系统创建工作空间记录
5. 用户自动成为Owner
6. 跳转到工作空间详情页

### 成员邀请流程
1. 管理员输入邀请邮箱和角色
2. 系统生成邀请令牌
3. 发送邀请邮件
4. 被邀请人点击邮件链接
5. 登录或注册账号
6. 确认接受邀请
7. 加入工作空间

### 权限变更流程
1. 管理员选择成员
2. 修改成员角色
3. 系统验证操作权限
4. 更新权限记录
5. 发送通知邮件
6. 实时更新界面权限

## 安全特性

### 数据隔离
- 基于工作空间的数据隔离
- 跨工作空间访问控制
- 敏感操作审计日志

### 权限验证
- 接口级权限验证
- 数据级权限过滤
- 前端权限控制

### 邀请安全
- 邀请令牌时效控制
- 邮箱验证机制
- 防止恶意邀请

## 测试用例

### 功能测试
- 工作空间CRUD操作测试
- 成员邀请流程测试
- 权限控制验证测试
- 邮件发送功能测试

### 权限测试
- 不同角色权限边界测试
- 跨工作空间访问控制测试
- 权限升级降级测试

### 安全测试
- 权限绕过攻击测试
- 邀请令牌安全测试
- 数据泄露防护测试

## 部署配置

### 邮件服务配置
```yaml
spring:
  mail:
    host: ${MAIL_HOST}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
```

### 权限缓存配置
```yaml
spring:
  cache:
    type: redis
    redis:
      time-to-live: 3600000 # 1小时
      cache-null-values: false
```

## 🚀 实施步骤

### 步骤1: 数据库扩展
```sql
-- 在v0.1基础上添加工作空间相关表

-- 工作空间表
CREATE TABLE workspaces (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    owner_id BIGINT NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    settings JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_id (owner_id),
    INDEX idx_name (name)
);

-- 工作空间成员表
CREATE TABLE workspace_members (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    user_id BIGINT NOT NULL,
    role ENUM('owner', 'admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT,
    invited_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP NULL,
    status ENUM('pending', 'active', 'inactive') DEFAULT 'pending',
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE SET NULL,
    UNIQUE KEY uk_workspace_user (workspace_id, user_id),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 工作空间邀请表
CREATE TABLE workspace_invitations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    workspace_id BIGINT NOT NULL,
    email VARCHAR(100) NOT NULL,
    role ENUM('admin', 'editor', 'viewer') DEFAULT 'viewer',
    invited_by BIGINT NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    status ENUM('pending', 'accepted', 'declined', 'expired') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    responded_at TIMESTAMP NULL,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    FOREIGN KEY (invited_by) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token (invitation_token),
    INDEX idx_email (email),
    INDEX idx_status (status)
);
```

### 步骤2: 后端核心代码实现
```java
// WorkspaceController.java - 工作空间控制器
@RestController
@RequestMapping("/api/workspaces")
@PreAuthorize("hasRole('USER')")
public class WorkspaceController {

    @Autowired
    private WorkspaceService workspaceService;

    @GetMapping
    public ResponseEntity<?> getUserWorkspaces(Authentication auth) {
        List<Workspace> workspaces = workspaceService.getUserWorkspaces(auth.getName());
        return ResponseEntity.ok(workspaces);
    }

    @PostMapping
    public ResponseEntity<?> createWorkspace(
        @RequestBody CreateWorkspaceRequest request,
        Authentication auth) {
        try {
            Workspace workspace = workspaceService.createWorkspace(request, auth.getName());
            return ResponseEntity.ok(workspace);
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("创建工作空间失败: " + e.getMessage()));
        }
    }

    @PostMapping("/{workspaceId}/members/invite")
    @PreAuthorize("@workspacePermissionService.hasPermission(#workspaceId, authentication.name, 'MANAGE_MEMBERS')")
    public ResponseEntity<?> inviteMember(
        @PathVariable Long workspaceId,
        @RequestBody InviteMemberRequest request,
        Authentication auth) {
        try {
            workspaceService.inviteMember(workspaceId, request, auth.getName());
            return ResponseEntity.ok(new ApiResponse("邀请发送成功"));
        } catch (Exception e) {
            return ResponseEntity.badRequest()
                .body(new ApiResponse("邀请失败: " + e.getMessage()));
        }
    }
}
```

### 步骤3: 前端核心组件实现
```vue
<!-- WorkspaceList.vue - 工作空间列表组件 -->
<template>
  <div class="workspace-list">
    <div class="workspace-header">
      <h2>我的工作空间</h2>
      <el-button type="primary" @click="showCreateDialog = true">
        <el-icon><Plus /></el-icon>
        创建工作空间
      </el-button>
    </div>

    <div class="workspace-grid">
      <el-card
        v-for="workspace in workspaces"
        :key="workspace.id"
        class="workspace-card"
        @click="enterWorkspace(workspace.id)"
      >
        <div class="workspace-info">
          <h3>{{ workspace.name }}</h3>
          <p>{{ workspace.description }}</p>
          <div class="workspace-meta">
            <el-tag :type="getRoleType(workspace.userRole)">
              {{ getRoleText(workspace.userRole) }}
            </el-tag>
            <span class="member-count">
              {{ workspace.memberCount }} 成员
            </span>
          </div>
        </div>
        <div class="workspace-actions" @click.stop>
          <el-dropdown>
            <el-button type="text">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item @click="editWorkspace(workspace)">
                  编辑
                </el-dropdown-item>
                <el-dropdown-item @click="manageMembers(workspace)">
                  成员管理
                </el-dropdown-item>
                <el-dropdown-item
                  v-if="workspace.userRole === 'owner'"
                  @click="deleteWorkspace(workspace)"
                  divided
                >
                  删除
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-card>
    </div>

    <!-- 创建工作空间对话框 -->
    <CreateWorkspaceDialog
      v-model="showCreateDialog"
      @created="handleWorkspaceCreated"
    />
  </div>
</template>
```

### 步骤4: 邮件服务配置
```yaml
# application.yml 添加邮件配置
spring:
  mail:
    host: smtp.gmail.com
    port: 587
    username: ${MAIL_USERNAME}
    password: ${MAIL_PASSWORD}
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true

# 邮件模板配置
mail:
  templates:
    invitation: |
      您好！

      您被邀请加入工作空间 "${workspaceName}"。

      点击以下链接接受邀请：
      ${invitationLink}

      此邀请将在24小时后过期。
```

## 🧪 可视化验证指南

### 验证步骤1: 工作空间管理
1. **创建工作空间**
   - 登录系统后，点击"创建工作空间"
   - 填写工作空间名称和描述
   - 点击创建，应该看到新工作空间出现在列表中

2. **编辑工作空间**
   - 点击工作空间卡片的更多按钮
   - 选择"编辑"，修改名称或描述
   - 保存后应该看到更新的信息

3. **工作空间切换**
   - 点击不同的工作空间卡片
   - 应该能够进入对应的工作空间
   - 顶部应该显示当前工作空间名称

### 验证步骤2: 成员管理
1. **邀请成员**
   - 在工作空间中点击"成员管理"
   - 点击"邀请成员"按钮
   - 输入邮箱地址和角色
   - 点击发送邀请

2. **查看成员列表**
   - 在成员管理页面查看所有成员
   - 应该显示成员姓名、邮箱、角色、状态

3. **修改成员权限**
   - 选择一个成员，点击"修改权限"
   - 更改角色后保存
   - 应该看到权限更新

### 验证步骤3: 邀请流程
1. **发送邀请邮件**
   - 邀请一个新用户
   - 检查邮箱是否收到邀请邮件
   - 邮件应该包含邀请链接

2. **接受邀请**
   - 点击邮件中的邀请链接
   - 如果是新用户，先注册账号
   - 登录后应该自动加入工作空间

3. **拒绝邀请**
   - 在邀请页面点击"拒绝"
   - 应该显示拒绝成功消息
   - 邀请状态应该更新为"已拒绝"

### 验证步骤4: 权限控制
1. **Owner权限验证**
   - 以Owner身份登录
   - 应该能够删除工作空间
   - 应该能够管理所有成员

2. **Admin权限验证**
   - 以Admin身份登录
   - 应该能够邀请和管理成员
   - 不应该能够删除工作空间

3. **Editor权限验证**
   - 以Editor身份登录
   - 应该能够查看工作空间
   - 不应该能够管理成员

4. **Viewer权限验证**
   - 以Viewer身份登录
   - 只能查看工作空间内容
   - 不能进行任何管理操作

## ✅ 验收标准

### 功能验收
- [x] 用户可以创建和管理工作空间
- [x] 可以邀请其他用户加入工作空间
- [x] 成员可以接受或拒绝邀请
- [x] 可以查看和管理工作空间成员
- [x] 可以修改成员权限和角色
- [x] 可以在多个工作空间之间切换

### 权限验收
- [x] 四级权限体系正确实现
- [x] Owner可以执行所有操作
- [x] Admin可以管理成员但不能删除工作空间
- [x] Editor只能查看和编辑内容
- [x] Viewer只有查看权限

### 技术验收
- [x] 工作空间权限控制正常工作
- [x] 数据隔离机制有效
- [x] 邮件通知功能正常
- [x] 所有API接口测试通过
- [x] 前端权限控制完善

### 用户体验验收
- [x] 界面操作直观友好
- [x] 权限提示清晰明确
- [x] 邀请流程简单易懂
- [x] 错误处理用户友好
- [x] 响应式设计适配良好
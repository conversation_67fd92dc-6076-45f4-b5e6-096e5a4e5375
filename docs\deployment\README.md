# AI-FDB 部署文档

本目录包含AI-FDB项目的部署相关文档和脚本。

## 文档列表

### Windows域局网共享权限设置

#### 📖 文档指南
- **[windows-domain-sharing.md](./windows-domain-sharing.md)** - 详细的Windows域局网共享权限设置指南
  - 包含完整的手动设置步骤
  - 权限配置详解
  - 安全建议和最佳实践
  - 常见问题解决方案

#### 🚀 自动化脚本
- **[setup-domain-sharing.ps1](./setup-domain-sharing.ps1)** - PowerShell自动化设置脚本
  - 一键设置共享权限
  - 支持自定义参数
  - 包含权限验证和报告生成
  
- **[setup-sharing.bat](./setup-sharing.bat)** - 用户友好的批处理脚本
  - 图形化交互界面
  - 自动调用PowerShell脚本
  - 适合非技术用户使用

#### 🔍 测试和故障排除
- **[test-sharing-permissions.ps1](./test-sharing-permissions.ps1)** - 权限验证和故障排除脚本
  - 全面的权限测试
  - 自动生成诊断报告
  - 提供故障排除建议

## 快速开始

### 方法一：使用批处理脚本（推荐）

1. **以管理员身份运行**
   ```cmd
   右键点击 setup-sharing.bat → 以管理员身份运行
   ```

2. **按提示输入信息**
   - 域名（例如：COMPANY）
   - 项目路径（默认：e:\AI-FDB）
   - 共享名称（默认：AI-FDB）
   - 用户组（默认：Domain Users）

3. **确认配置并执行**

### 方法二：使用PowerShell脚本

1. **打开PowerShell（管理员）**
   ```powershell
   # 导航到部署目录
   cd "e:\AI-FDB\docs\deployment"
   
   # 执行设置脚本
   .\setup-domain-sharing.ps1 -DomainName "YOURDOMAIN"
   ```

2. **可选参数**
   ```powershell
   .\setup-domain-sharing.ps1 `
       -DomainName "COMPANY" `
       -ProjectPath "e:\AI-FDB" `
       -ShareName "AI-FDB" `
       -UserGroup "Domain Users"
   ```

### 方法三：手动设置

参考 [windows-domain-sharing.md](./windows-domain-sharing.md) 文档进行手动配置。

## 验证设置

设置完成后，运行测试脚本验证权限：

```powershell
# 以管理员身份运行PowerShell
.\test-sharing-permissions.ps1
```

测试脚本将：
- 检查共享是否存在
- 验证共享权限设置
- 检查NTFS权限
- 测试网络配置
- 验证域连接
- 测试文件操作权限
- 生成详细的诊断报告

## 访问共享

设置完成后，域内用户可通过以下方式访问：

### UNC路径访问
```
\\计算机名\AI-FDB
\\IP地址\AI-FDB
```

### 映射网络驱动器
1. 打开文件资源管理器
2. 右键点击"此电脑" → "映射网络驱动器"
3. 选择驱动器号
4. 输入文件夹路径：`\\计算机名\AI-FDB`
5. 勾选"登录时重新连接"

## 故障排除

### 常见问题

1. **无法访问共享**
   - 检查网络连接
   - 验证防火墙设置
   - 确认共享名称正确

2. **权限被拒绝**
   - 检查用户组成员身份
   - 验证NTFS权限设置
   - 确认共享权限配置

3. **网络发现问题**
   - 启用网络发现
   - 检查网络配置文件
   - 确认防火墙规则

### 获取帮助

1. **运行诊断脚本**
   ```powershell
   .\test-sharing-permissions.ps1
   ```

2. **查看生成的诊断报告**
   - 报告位置：`docs/deployment/diagnostic-report.html`
   - 包含详细的测试结果和建议

3. **检查权限报告**
   - 报告位置：`docs/deployment/permission-report.txt`
   - 包含当前权限配置详情

## 安全注意事项

1. **最小权限原则**
   - 只授予必要的权限
   - 使用特定用户组而非"Everyone"
   - 定期审查权限设置

2. **审计和监控**
   - 启用文件访问审计
   - 定期检查访问日志
   - 监控异常访问行为

3. **备份和恢复**
   - 定期备份权限配置
   - 建立权限恢复流程
   - 测试备份有效性

## 维护建议

1. **定期检查**
   - 每月运行权限测试脚本
   - 检查用户组成员变化
   - 验证网络配置

2. **文档更新**
   - 记录权限变更
   - 更新用户访问指南
   - 维护故障排除知识库

3. **培训支持**
   - 培训用户正确访问方式
   - 提供技术支持联系方式
   - 建立问题反馈机制

---

**注意：** 所有脚本都需要管理员权限运行，请确保在执行前备份重要数据。
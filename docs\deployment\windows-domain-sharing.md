# Windows域局网共享权限设置指南

## 概述
本文档指导如何在Windows域环境下设置AI-FDB项目的共享权限，使域内用户可以访问和编辑项目文件。

## 前置条件
- Windows Server或Windows 10/11 Pro版本
- 计算机已加入域
- 具有管理员权限
- 项目路径：`e:\AI-FDB`

## 设置步骤

### 1. 创建共享文件夹

#### 1.1 右键点击项目文件夹
1. 在文件资源管理器中导航到 `e:\AI-FDB`
2. 右键点击 `AI-FDB` 文件夹
3. 选择 "属性"

#### 1.2 配置共享设置
1. 点击 "共享" 选项卡
2. 点击 "高级共享" 按钮
3. 勾选 "共享此文件夹"
4. 设置共享名称：`AI-FDB` (或自定义名称)
5. 点击 "权限" 按钮

### 2. 设置共享权限

#### 2.1 移除默认权限
1. 选中 "Everyone" 用户组
2. 点击 "删除" 按钮

#### 2.2 添加域用户组权限
1. 点击 "添加" 按钮
2. 在 "选择用户或组" 对话框中输入：
   - `Domain Users` (域用户组)
   - 或特定的域用户组名称
3. 点击 "检查名称" 验证
4. 点击 "确定"

#### 2.3 设置权限级别
为添加的域用户组设置以下权限：
- **完全控制**：✓ (如需完整编辑权限)
- **更改**：✓
- **读取**：✓

### 3. 设置NTFS文件系统权限

#### 3.1 配置安全权限
1. 在 "属性" 对话框中点击 "安全" 选项卡
2. 点击 "编辑" 按钮
3. 点击 "添加" 按钮

#### 3.2 添加域用户权限
1. 输入域用户组：`DOMAIN\Domain Users`
   - 替换 `DOMAIN` 为实际域名
2. 点击 "检查名称" 验证
3. 点击 "确定"

#### 3.3 设置详细权限
为域用户组设置以下NTFS权限：
- **完全控制**：✓
- **修改**：✓
- **读取和执行**：✓
- **列出文件夹内容**：✓
- **读取**：✓
- **写入**：✓

### 4. 高级权限配置

#### 4.1 继承权限设置
1. 点击 "高级" 按钮
2. 确保勾选 "包括可从此对象的父项继承的权限"
3. 勾选 "将此对象的权限替换为可从父项继承的权限"

#### 4.2 子文件夹权限继承
1. 选择域用户组权限条目
2. 点击 "编辑"
3. 在 "应用到" 下拉菜单中选择：
   - "此文件夹、子文件夹和文件"

### 5. 网络发现和文件共享设置

#### 5.1 启用网络发现
1. 打开 "控制面板" → "网络和共享中心"
2. 点击 "更改高级共享设置"
3. 展开当前网络配置文件
4. 启用以下选项：
   - **启用网络发现**
   - **启用文件和打印机共享**
   - **启用共享以便可以访问网络的用户可以读取和写入公用文件夹中的文件**

#### 5.2 密码保护共享
在 "所有网络" 部分：
- 选择 "关闭密码保护共享" (域环境推荐)

### 6. 防火墙配置

#### 6.1 允许文件和打印机共享
1. 打开 "Windows Defender 防火墙"
2. 点击 "允许应用或功能通过Windows Defender防火墙"
3. 确保勾选 "文件和打印机共享" 的域和专用网络选项

### 7. 域用户访问方式

#### 7.1 UNC路径访问
域内用户可通过以下方式访问：
```
\\计算机名\AI-FDB
\\IP地址\AI-FDB
```

#### 7.2 映射网络驱动器
1. 打开文件资源管理器
2. 右键点击 "此电脑"
3. 选择 "映射网络驱动器"
4. 选择驱动器号
5. 输入文件夹路径：`\\计算机名\AI-FDB`
6. 勾选 "登录时重新连接"

### 8. 权限验证

#### 8.1 测试访问
1. 从域内其他计算机尝试访问共享
2. 验证读取权限：打开文件
3. 验证写入权限：创建测试文件
4. 验证修改权限：编辑现有文件

#### 8.2 权限故障排除
如果遇到访问问题：
1. 检查域用户是否在正确的用户组中
2. 验证计算机是否正确加入域
3. 检查网络连接和DNS解析
4. 查看事件查看器中的安全日志

## 安全建议

### 1. 最小权限原则
- 只给予必要的权限
- 定期审查权限设置
- 使用特定的用户组而非 "Everyone"

### 2. 审计设置
1. 在 "高级安全设置" 中配置审计
2. 启用文件访问审计
3. 定期检查审计日志

### 3. 备份权限配置
使用以下PowerShell命令备份权限：
```powershell
# 导出共享权限
Get-SmbShare -Name "AI-FDB" | Get-SmbShareAccess | Export-Csv -Path "C:\backup\share-permissions.csv"

# 导出NTFS权限
Get-Acl -Path "e:\AI-FDB" | Export-Clixml -Path "C:\backup\ntfs-permissions.xml"
```

## 常见问题解决

### Q1: 无法访问共享文件夹
**解决方案：**
1. 检查网络连接
2. 验证共享名称拼写
3. 确认防火墙设置
4. 检查用户权限

### Q2: 可以读取但无法编辑文件
**解决方案：**
1. 检查NTFS权限中的 "写入" 权限
2. 验证共享权限中的 "更改" 权限
3. 确认文件不是只读属性

### Q3: 权限设置后仍无法访问
**解决方案：**
1. 重启计算机使权限生效
2. 清除网络凭据缓存
3. 重新加入域

## 维护建议

1. **定期权限审查**：每季度检查权限设置
2. **用户组管理**：及时添加/移除用户
3. **监控访问日志**：定期查看访问记录
4. **备份配置**：定期备份权限设置

---

**注意事项：**
- 本指南适用于Windows域环境
- 具体步骤可能因Windows版本而略有差异
- 建议在测试环境中先行验证
- 重要数据请提前备份
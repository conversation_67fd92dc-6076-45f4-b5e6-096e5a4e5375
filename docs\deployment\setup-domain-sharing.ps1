# AI-FDB 域共享权限自动设置脚本
# 需要管理员权限运行

#Requires -RunAsAdministrator

param(
    [Parameter(Mandatory=$true)]
    [string]$ProjectPath = "e:\AI-FDB",
    
    [Parameter(Mandatory=$true)]
    [string]$DomainName,
    
    [Parameter(Mandatory=$false)]
    [string]$ShareName = "AI-FDB",
    
    [Parameter(Mandatory=$false)]
    [string]$UserGroup = "Domain Users"
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 错误处理函数
function Handle-Error {
    param([string]$ErrorMessage)
    Write-ColorOutput "错误: $ErrorMessage" "Red"
    exit 1
}

# 检查前置条件
function Test-Prerequisites {
    Write-ColorOutput "检查前置条件..." "Yellow"
    
    # 检查路径是否存在
    if (-not (Test-Path $ProjectPath)) {
        Handle-Error "项目路径不存在: $ProjectPath"
    }
    
    # 检查是否加入域
    $computerSystem = Get-WmiObject -Class Win32_ComputerSystem
    if ($computerSystem.PartOfDomain -eq $false) {
        Handle-Error "计算机未加入域"
    }
    
    # 检查域名
    if ($computerSystem.Domain -ne $DomainName) {
        Write-ColorOutput "警告: 当前域 ($($computerSystem.Domain)) 与指定域 ($DomainName) 不匹配" "Yellow"
    }
    
    Write-ColorOutput "前置条件检查完成" "Green"
}

# 创建共享文件夹
function New-ProjectShare {
    Write-ColorOutput "创建共享文件夹..." "Yellow"
    
    try {
        # 检查共享是否已存在
        $existingShare = Get-SmbShare -Name $ShareName -ErrorAction SilentlyContinue
        if ($existingShare) {
            Write-ColorOutput "共享 '$ShareName' 已存在，正在移除..." "Yellow"
            Remove-SmbShare -Name $ShareName -Force
        }
        
        # 创建新共享
        New-SmbShare -Name $ShareName -Path $ProjectPath -FullAccess "$DomainName\$UserGroup" -Description "AI-FDB项目共享文件夹"
        Write-ColorOutput "共享文件夹创建成功" "Green"
        
    } catch {
        Handle-Error "创建共享文件夹失败: $($_.Exception.Message)"
    }
}

# 设置NTFS权限
function Set-NTFSPermissions {
    Write-ColorOutput "设置NTFS权限..." "Yellow"
    
    try {
        # 获取当前ACL
        $acl = Get-Acl $ProjectPath
        
        # 创建新的访问规则
        $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule(
            "$DomainName\$UserGroup",
            "FullControl",
            "ContainerInherit,ObjectInherit",
            "None",
            "Allow"
        )
        
        # 添加访问规则
        $acl.SetAccessRule($accessRule)
        
        # 应用ACL
        Set-Acl -Path $ProjectPath -AclObject $acl
        
        Write-ColorOutput "NTFS权限设置成功" "Green"
        
    } catch {
        Handle-Error "设置NTFS权限失败: $($_.Exception.Message)"
    }
}

# 配置网络发现和文件共享
function Enable-NetworkSharing {
    Write-ColorOutput "配置网络发现和文件共享..." "Yellow"
    
    try {
        # 启用网络发现
        netsh advfirewall firewall set rule group="Network Discovery" new enable=Yes
        
        # 启用文件和打印机共享
        netsh advfirewall firewall set rule group="File and Printer Sharing" new enable=Yes
        
        # 设置网络配置文件
        $networkProfiles = Get-NetConnectionProfile
        foreach ($profile in $networkProfiles) {
            if ($profile.NetworkCategory -eq "Public") {
                Set-NetConnectionProfile -InterfaceIndex $profile.InterfaceIndex -NetworkCategory Private
                Write-ColorOutput "网络配置文件已更改为专用" "Green"
            }
        }
        
        Write-ColorOutput "网络共享配置完成" "Green"
        
    } catch {
        Write-ColorOutput "警告: 网络共享配置可能未完全成功: $($_.Exception.Message)" "Yellow"
    }
}

# 验证权限设置
function Test-Permissions {
    Write-ColorOutput "验证权限设置..." "Yellow"
    
    try {
        # 检查共享权限
        $shareAccess = Get-SmbShareAccess -Name $ShareName
        $domainUserAccess = $shareAccess | Where-Object { $_.AccountName -eq "$DomainName\$UserGroup" }
        
        if ($domainUserAccess -and $domainUserAccess.AccessRight -eq "Full") {
            Write-ColorOutput "共享权限验证成功" "Green"
        } else {
            Write-ColorOutput "警告: 共享权限可能未正确设置" "Yellow"
        }
        
        # 检查NTFS权限
        $acl = Get-Acl $ProjectPath
        $domainUserRule = $acl.Access | Where-Object { 
            $_.IdentityReference -eq "$DomainName\$UserGroup" -and 
            $_.FileSystemRights -match "FullControl"
        }
        
        if ($domainUserRule) {
            Write-ColorOutput "NTFS权限验证成功" "Green"
        } else {
            Write-ColorOutput "警告: NTFS权限可能未正确设置" "Yellow"
        }
        
    } catch {
        Write-ColorOutput "权限验证过程中出现错误: $($_.Exception.Message)" "Yellow"
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-ColorOutput "`n=== 访问信息 ===" "Cyan"
    
    $computerName = $env:COMPUTERNAME
    $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -ne "127.0.0.1" }).IPAddress | Select-Object -First 1
    
    Write-ColorOutput "共享名称: $ShareName" "White"
    Write-ColorOutput "UNC路径: \\$computerName\$ShareName" "White"
    Write-ColorOutput "IP访问: \\$ipAddress\$ShareName" "White"
    Write-ColorOutput "本地路径: $ProjectPath" "White"
    Write-ColorOutput "授权用户组: $DomainName\$UserGroup" "White"
    
    Write-ColorOutput "`n域用户可以通过以下方式访问:" "Cyan"
    Write-ColorOutput "1. 在文件资源管理器地址栏输入: \\$computerName\$ShareName" "White"
    Write-ColorOutput "2. 映射网络驱动器到: \\$computerName\$ShareName" "White"
    Write-ColorOutput "3. 通过IP地址访问: \\$ipAddress\$ShareName" "White"
}

# 生成权限报告
function Export-PermissionReport {
    Write-ColorOutput "生成权限报告..." "Yellow"
    
    $reportPath = Join-Path $ProjectPath "docs\deployment\permission-report.txt"
    $reportContent = @()
    
    $reportContent += "AI-FDB 域共享权限报告"
    $reportContent += "生成时间: $(Get-Date)"
    $reportContent += "="*50
    $reportContent += ""
    
    # 共享信息
    $reportContent += "共享信息:"
    $shareInfo = Get-SmbShare -Name $ShareName
    $reportContent += "  名称: $($shareInfo.Name)"
    $reportContent += "  路径: $($shareInfo.Path)"
    $reportContent += "  描述: $($shareInfo.Description)"
    $reportContent += ""
    
    # 共享权限
    $reportContent += "共享权限:"
    $shareAccess = Get-SmbShareAccess -Name $ShareName
    foreach ($access in $shareAccess) {
        $reportContent += "  $($access.AccountName): $($access.AccessRight)"
    }
    $reportContent += ""
    
    # NTFS权限
    $reportContent += "NTFS权限:"
    $acl = Get-Acl $ProjectPath
    foreach ($access in $acl.Access) {
        if ($access.IdentityReference -like "*$DomainName*") {
            $reportContent += "  $($access.IdentityReference): $($access.FileSystemRights)"
        }
    }
    
    # 保存报告
    $reportContent | Out-File -FilePath $reportPath -Encoding UTF8
    Write-ColorOutput "权限报告已保存到: $reportPath" "Green"
}

# 主执行流程
function Main {
    Write-ColorOutput "AI-FDB 域共享权限设置脚本" "Cyan"
    Write-ColorOutput "项目路径: $ProjectPath" "White"
    Write-ColorOutput "域名: $DomainName" "White"
    Write-ColorOutput "用户组: $UserGroup" "White"
    Write-ColorOutput "共享名: $ShareName" "White"
    Write-ColorOutput ""
    
    # 执行设置步骤
    Test-Prerequisites
    New-ProjectShare
    Set-NTFSPermissions
    Enable-NetworkSharing
    Test-Permissions
    Export-PermissionReport
    Show-AccessInfo
    
    Write-ColorOutput "`n域共享权限设置完成!" "Green"
    Write-ColorOutput "请在域内其他计算机上测试访问权限" "Yellow"
}

# 脚本使用说明
function Show-Usage {
    Write-ColorOutput "使用方法:" "Cyan"
    Write-ColorOutput "  .\setup-domain-sharing.ps1 -DomainName 'YOURDOMAIN' [-ProjectPath 'e:\AI-FDB'] [-ShareName 'AI-FDB'] [-UserGroup 'Domain Users']" "White"
    Write-ColorOutput ""
    Write-ColorOutput "参数说明:" "Cyan"
    Write-ColorOutput "  -DomainName   : 域名 (必需)" "White"
    Write-ColorOutput "  -ProjectPath  : 项目路径 (可选，默认: e:\AI-FDB)" "White"
    Write-ColorOutput "  -ShareName    : 共享名称 (可选，默认: AI-FDB)" "White"
    Write-ColorOutput "  -UserGroup    : 用户组 (可选，默认: Domain Users)" "White"
    Write-ColorOutput ""
    Write-ColorOutput "示例:" "Cyan"
    Write-ColorOutput "  .\setup-domain-sharing.ps1 -DomainName 'COMPANY'" "White"
    Write-ColorOutput "  .\setup-domain-sharing.ps1 -DomainName 'COMPANY' -UserGroup 'AI-FDB Users'" "White"
}

# 检查是否提供了必需参数
if (-not $DomainName) {
    Show-Usage
    exit 1
}

# 执行主程序
try {
    Main
} catch {
    Handle-Error "脚本执行失败: $($_.Exception.Message)"
}
# AI-FDB 域共享权限测试和故障排除脚本
# 用于验证共享权限设置是否正确

#Requires -RunAsAdministrator

param(
    [Parameter(Mandatory=$false)]
    [string]$ShareName = "AI-FDB",
    
    [Parameter(Mandatory=$false)]
    [string]$ProjectPath = "e:\AI-FDB",
    
    [Parameter(Mandatory=$false)]
    [string]$TestUser = $null
)

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

# 测试结果记录
$TestResults = @()

function Add-TestResult {
    param(
        [string]$TestName,
        [bool]$Passed,
        [string]$Details = ""
    )
    
    $result = [PSCustomObject]@{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    
    $script:TestResults += $result
    
    $status = if ($Passed) { "通过" } else { "失败" }
    $color = if ($Passed) { "Green" } else { "Red" }
    
    Write-ColorOutput "[$status] $TestName" $color
    if ($Details) {
        Write-ColorOutput "    详情: $Details" "Gray"
    }
}

# 测试1: 检查共享是否存在
function Test-ShareExists {
    Write-ColorOutput "`n=== 测试1: 检查共享是否存在 ===" "Cyan"
    
    try {
        $share = Get-SmbShare -Name $ShareName -ErrorAction Stop
        Add-TestResult "共享存在性检查" $true "共享 '$ShareName' 存在，路径: $($share.Path)"
        return $share
    } catch {
        Add-TestResult "共享存在性检查" $false "共享 '$ShareName' 不存在: $($_.Exception.Message)"
        return $null
    }
}

# 测试2: 检查共享权限
function Test-SharePermissions {
    param($Share)
    
    Write-ColorOutput "`n=== 测试2: 检查共享权限 ===" "Cyan"
    
    if (-not $Share) {
        Add-TestResult "共享权限检查" $false "无法检查权限，共享不存在"
        return
    }
    
    try {
        $shareAccess = Get-SmbShareAccess -Name $ShareName
        
        Write-ColorOutput "当前共享权限:" "Yellow"
        foreach ($access in $shareAccess) {
            Write-ColorOutput "  $($access.AccountName): $($access.AccessRight)" "White"
        }
        
        # 检查是否有域用户权限
        $domainAccess = $shareAccess | Where-Object { $_.AccountName -like "*\Domain Users" -or $_.AccountName -like "*\*" }
        
        if ($domainAccess) {
            Add-TestResult "域用户共享权限" $true "找到域用户权限设置"
        } else {
            Add-TestResult "域用户共享权限" $false "未找到域用户权限设置"
        }
        
    } catch {
        Add-TestResult "共享权限检查" $false "无法获取共享权限: $($_.Exception.Message)"
    }
}

# 测试3: 检查NTFS权限
function Test-NTFSPermissions {
    Write-ColorOutput "`n=== 测试3: 检查NTFS权限 ===" "Cyan"
    
    if (-not (Test-Path $ProjectPath)) {
        Add-TestResult "NTFS权限检查" $false "项目路径不存在: $ProjectPath"
        return
    }
    
    try {
        $acl = Get-Acl $ProjectPath
        
        Write-ColorOutput "当前NTFS权限:" "Yellow"
        foreach ($access in $acl.Access) {
            if ($access.IdentityReference -like "*\*") {
                Write-ColorOutput "  $($access.IdentityReference): $($access.FileSystemRights)" "White"
            }
        }
        
        # 检查域用户权限
        $domainUserAccess = $acl.Access | Where-Object { 
            $_.IdentityReference -like "*\Domain Users" -and 
            $_.FileSystemRights -match "FullControl|Modify|Write"
        }
        
        if ($domainUserAccess) {
            Add-TestResult "域用户NTFS权限" $true "域用户具有适当的NTFS权限"
        } else {
            Add-TestResult "域用户NTFS权限" $false "域用户缺少必要的NTFS权限"
        }
        
    } catch {
        Add-TestResult "NTFS权限检查" $false "无法获取NTFS权限: $($_.Exception.Message)"
    }
}

# 测试4: 检查网络配置
function Test-NetworkConfiguration {
    Write-ColorOutput "`n=== 测试4: 检查网络配置 ===" "Cyan"
    
    # 检查网络发现
    try {
        $networkDiscovery = netsh advfirewall firewall show rule name="Network Discovery" | Select-String "Enabled.*Yes"
        if ($networkDiscovery) {
            Add-TestResult "网络发现" $true "网络发现已启用"
        } else {
            Add-TestResult "网络发现" $false "网络发现未启用"
        }
    } catch {
        Add-TestResult "网络发现" $false "无法检查网络发现状态"
    }
    
    # 检查文件和打印机共享
    try {
        $fileSharing = netsh advfirewall firewall show rule name="File and Printer Sharing" | Select-String "Enabled.*Yes"
        if ($fileSharing) {
            Add-TestResult "文件和打印机共享" $true "文件和打印机共享已启用"
        } else {
            Add-TestResult "文件和打印机共享" $false "文件和打印机共享未启用"
        }
    } catch {
        Add-TestResult "文件和打印机共享" $false "无法检查文件共享状态"
    }
    
    # 检查网络配置文件
    try {
        $networkProfiles = Get-NetConnectionProfile
        $publicProfiles = $networkProfiles | Where-Object { $_.NetworkCategory -eq "Public" }
        
        if ($publicProfiles) {
            Add-TestResult "网络配置文件" $false "存在公用网络配置文件，可能影响共享"
            Write-ColorOutput "    建议将网络配置文件更改为专用" "Yellow"
        } else {
            Add-TestResult "网络配置文件" $true "网络配置文件设置正确"
        }
    } catch {
        Add-TestResult "网络配置文件" $false "无法检查网络配置文件"
    }
}

# 测试5: 检查域连接
function Test-DomainConnection {
    Write-ColorOutput "`n=== 测试5: 检查域连接 ===" "Cyan"
    
    try {
        $computerSystem = Get-WmiObject -Class Win32_ComputerSystem
        
        if ($computerSystem.PartOfDomain) {
            Add-TestResult "域成员身份" $true "计算机已加入域: $($computerSystem.Domain)"
            
            # 测试域控制器连接
            try {
                $domainController = nltest /dsgetdc:$($computerSystem.Domain) 2>$null
                if ($LASTEXITCODE -eq 0) {
                    Add-TestResult "域控制器连接" $true "可以连接到域控制器"
                } else {
                    Add-TestResult "域控制器连接" $false "无法连接到域控制器"
                }
            } catch {
                Add-TestResult "域控制器连接" $false "域控制器连接测试失败"
            }
            
        } else {
            Add-TestResult "域成员身份" $false "计算机未加入域"
        }
        
    } catch {
        Add-TestResult "域连接检查" $false "无法检查域连接状态: $($_.Exception.Message)"
    }
}

# 测试6: 网络连通性测试
function Test-NetworkConnectivity {
    Write-ColorOutput "`n=== 测试6: 网络连通性测试 ===" "Cyan"
    
    $computerName = $env:COMPUTERNAME
    $ipAddress = (Get-NetIPAddress -AddressFamily IPv4 | Where-Object { $_.IPAddress -ne "127.0.0.1" }).IPAddress | Select-Object -First 1
    
    Write-ColorOutput "本机信息:" "Yellow"
    Write-ColorOutput "  计算机名: $computerName" "White"
    Write-ColorOutput "  IP地址: $ipAddress" "White"
    
    # 测试本地访问
    try {
        $localAccess = Test-Path "\\$computerName\$ShareName" -ErrorAction Stop
        Add-TestResult "本地UNC访问" $true "可以通过UNC路径访问共享"
    } catch {
        Add-TestResult "本地UNC访问" $false "无法通过UNC路径访问共享: $($_.Exception.Message)"
    }
    
    # 测试IP访问
    try {
        $ipAccess = Test-Path "\\$ipAddress\$ShareName" -ErrorAction Stop
        Add-TestResult "IP地址访问" $true "可以通过IP地址访问共享"
    } catch {
        Add-TestResult "IP地址访问" $false "无法通过IP地址访问共享: $($_.Exception.Message)"
    }
}

# 测试7: 文件操作权限测试
function Test-FileOperations {
    Write-ColorOutput "`n=== 测试7: 文件操作权限测试 ===" "Cyan"
    
    $testFilePath = Join-Path $ProjectPath "test-permissions.txt"
    
    # 测试写入权限
    try {
        "权限测试文件 - $(Get-Date)" | Out-File -FilePath $testFilePath -ErrorAction Stop
        Add-TestResult "文件写入权限" $true "可以创建和写入文件"
        
        # 测试读取权限
        $content = Get-Content $testFilePath -ErrorAction Stop
        Add-TestResult "文件读取权限" $true "可以读取文件内容"
        
        # 测试删除权限
        Remove-Item $testFilePath -ErrorAction Stop
        Add-TestResult "文件删除权限" $true "可以删除文件"
        
    } catch {
        Add-TestResult "文件操作权限" $false "文件操作失败: $($_.Exception.Message)"
    }
}

# 生成诊断报告
function Export-DiagnosticReport {
    Write-ColorOutput "`n=== 生成诊断报告 ===" "Cyan"
    
    $reportPath = Join-Path $ProjectPath "docs\deployment\diagnostic-report.html"
    
    $html = @"
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>AI-FDB 域共享权限诊断报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #f0f0f0; padding: 10px; border-radius: 5px; }
        .passed { color: green; }
        .failed { color: red; }
        .test-item { margin: 10px 0; padding: 10px; border-left: 3px solid #ccc; }
        .test-passed { border-left-color: green; }
        .test-failed { border-left-color: red; }
        .details { font-size: 0.9em; color: #666; margin-top: 5px; }
        .summary { background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="header">
        <h1>AI-FDB 域共享权限诊断报告</h1>
        <p>生成时间: $(Get-Date)</p>
        <p>计算机: $env:COMPUTERNAME</p>
        <p>共享名称: $ShareName</p>
        <p>项目路径: $ProjectPath</p>
    </div>
    
    <div class="summary">
        <h2>测试摘要</h2>
        <p>总测试数: $($TestResults.Count)</p>
        <p>通过: $($TestResults | Where-Object { $_.Passed }).Count</p>
        <p>失败: $($TestResults | Where-Object { -not $_.Passed }).Count</p>
    </div>
    
    <h2>详细测试结果</h2>
"@
    
    foreach ($result in $TestResults) {
        $statusClass = if ($result.Passed) { "test-passed" } else { "test-failed" }
        $statusText = if ($result.Passed) { "通过" } else { "失败" }
        $statusColor = if ($result.Passed) { "passed" } else { "failed" }
        
        $html += @"
    <div class="test-item $statusClass">
        <strong>$($result.TestName)</strong> - <span class="$statusColor">$statusText</span>
        <div class="details">时间: $($result.Timestamp)</div>
"@
        
        if ($result.Details) {
            $html += "        <div class=`"details`">$($result.Details)</div>`n"
        }
        
        $html += "    </div>`n"
    }
    
    $html += @"
    
    <h2>故障排除建议</h2>
    <div class="test-item">
        <h3>如果测试失败，请尝试以下解决方案：</h3>
        <ul>
            <li><strong>共享不存在</strong>: 运行 setup-domain-sharing.ps1 脚本创建共享</li>
            <li><strong>权限问题</strong>: 检查NTFS权限和共享权限设置</li>
            <li><strong>网络问题</strong>: 启用网络发现和文件共享，检查防火墙设置</li>
            <li><strong>域连接问题</strong>: 确保计算机正确加入域，检查域控制器连接</li>
            <li><strong>访问被拒绝</strong>: 检查用户组成员身份，确认权限继承设置</li>
        </ul>
    </div>
    
    <h2>有用的命令</h2>
    <div class="test-item">
        <h3>PowerShell命令:</h3>
        <pre>
# 查看共享列表
Get-SmbShare

# 查看共享权限
Get-SmbShareAccess -Name "$ShareName"

# 查看NTFS权限
Get-Acl "$ProjectPath"

# 测试网络连接
Test-NetConnection -ComputerName $env:COMPUTERNAME -Port 445

# 重新加载权限
gpupdate /force
        </pre>
    </div>
</body>
</html>
"@
    
    try {
        $html | Out-File -FilePath $reportPath -Encoding UTF8
        Write-ColorOutput "诊断报告已保存到: $reportPath" "Green"
        
        # 尝试打开报告
        if (Test-Path $reportPath) {
            Start-Process $reportPath
        }
    } catch {
        Write-ColorOutput "保存诊断报告失败: $($_.Exception.Message)" "Red"
    }
}

# 主执行函数
function Main {
    Write-ColorOutput "AI-FDB 域共享权限测试和故障排除" "Cyan"
    Write-ColorOutput "共享名称: $ShareName" "White"
    Write-ColorOutput "项目路径: $ProjectPath" "White"
    Write-ColorOutput ""
    
    # 执行所有测试
    $share = Test-ShareExists
    Test-SharePermissions $share
    Test-NTFSPermissions
    Test-NetworkConfiguration
    Test-DomainConnection
    Test-NetworkConnectivity
    Test-FileOperations
    
    # 生成报告
    Export-DiagnosticReport
    
    # 显示总结
    Write-ColorOutput "`n=== 测试总结 ===" "Cyan"
    $passedTests = $TestResults | Where-Object { $_.Passed }
    $failedTests = $TestResults | Where-Object { -not $_.Passed }
    
    Write-ColorOutput "总测试数: $($TestResults.Count)" "White"
    Write-ColorOutput "通过: $($passedTests.Count)" "Green"
    Write-ColorOutput "失败: $($failedTests.Count)" "Red"
    
    if ($failedTests.Count -gt 0) {
        Write-ColorOutput "`n失败的测试:" "Red"
        foreach ($test in $failedTests) {
            Write-ColorOutput "  - $($test.TestName)" "Red"
        }
        Write-ColorOutput "`n请查看诊断报告获取详细的故障排除建议" "Yellow"
    } else {
        Write-ColorOutput "`n所有测试通过! 域共享权限配置正确" "Green"
    }
}

# 执行主程序
try {
    Main
} catch {
    Write-ColorOutput "测试脚本执行失败: $($_.Exception.Message)" "Red"
    exit 1
}
@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM AI-FDB 域共享权限设置批处理脚本
REM 此脚本将调用PowerShell脚本来设置域共享权限

title AI-FDB 域共享权限设置

echo.
echo ========================================
echo     AI-FDB 域共享权限设置工具
echo ========================================
echo.

REM 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo [错误] 此脚本需要管理员权限运行
    echo 请右键点击此文件，选择"以管理员身份运行"
    echo.
    pause
    exit /b 1
)

echo [信息] 管理员权限验证成功
echo.

REM 获取用户输入
set /p DOMAIN_NAME="请输入域名 (例如: COMPANY): "
if "!DOMAIN_NAME!"=="" (
    echo [错误] 域名不能为空
    pause
    exit /b 1
)

echo.
set /p PROJECT_PATH="请输入项目路径 (默认: e:\AI-FDB): "
if "!PROJECT_PATH!"=="" set PROJECT_PATH=e:\AI-FDB

echo.
set /p SHARE_NAME="请输入共享名称 (默认: AI-FDB): "
if "!SHARE_NAME!"=="" set SHARE_NAME=AI-FDB

echo.
set /p USER_GROUP="请输入用户组名称 (默认: Domain Users): "
if "!USER_GROUP!"=="" set USER_GROUP=Domain Users

echo.
echo ========================================
echo 配置确认:
echo ========================================
echo 域名: !DOMAIN_NAME!
echo 项目路径: !PROJECT_PATH!
echo 共享名称: !SHARE_NAME!
echo 用户组: !USER_GROUP!
echo ========================================
echo.

set /p CONFIRM="确认以上配置? (Y/N): "
if /i "!CONFIRM!" neq "Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo [信息] 开始执行PowerShell脚本...
echo.

REM 检查PowerShell脚本是否存在
set PS_SCRIPT=%~dp0setup-domain-sharing.ps1
if not exist "!PS_SCRIPT!" (
    echo [错误] PowerShell脚本不存在: !PS_SCRIPT!
    echo 请确保 setup-domain-sharing.ps1 文件在同一目录下
    pause
    exit /b 1
)

REM 执行PowerShell脚本
powershell.exe -ExecutionPolicy Bypass -File "!PS_SCRIPT!" -DomainName "!DOMAIN_NAME!" -ProjectPath "!PROJECT_PATH!" -ShareName "!SHARE_NAME!" -UserGroup "!USER_GROUP!"

if %errorLevel% equ 0 (
    echo.
    echo ========================================
    echo 域共享权限设置完成!
    echo ========================================
    echo.
    echo 访问方式:
    echo 1. UNC路径: \\%COMPUTERNAME%\!SHARE_NAME!
    echo 2. 映射网络驱动器到上述路径
    echo.
    echo 请在域内其他计算机上测试访问权限
    echo.
) else (
    echo.
    echo [错误] PowerShell脚本执行失败
    echo 请检查错误信息并重试
    echo.
)

echo 按任意键退出...
pause >nul
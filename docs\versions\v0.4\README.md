# AI-FDB v0.4 - 数据录入

## 版本概述

基于已定义的数据表结构，实现数据录入功能，包括手动录入、批量导入、AI辅助录入等多种方式，为用户提供灵活高效的数据录入体验。

## 功能模块

### 1. 数据录入管理
- 单条数据录入
- 批量数据录入
- 数据编辑和删除
- 录入历史记录

### 2. 导入导出功能
- Excel/CSV文件导入
- 数据模板下载
- 导入结果验证
- 错误数据处理

### 3. AI辅助录入
- 智能数据识别
- 自动字段填充
- 数据格式转换
- 重复数据检测

### 4. 数据验证
- 实时字段验证
- 数据完整性检查
- 业务规则验证
- 错误提示和修正

## 技术实现

### 新增技术组件
- Apache POI (Excel处理)
- OpenCSV (CSV处理)
- 数据验证引擎
- 批量处理框架
- 文件上传服务

### 核心特性
- 动态表单生成
- 实时数据验证
- 批量操作优化
- 错误恢复机制

## 数据库设计

### 新增表结构

#### data_records 表
```sql
CREATE TABLE data_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    record_data JSON NOT NULL COMMENT '记录数据',
    record_hash VARCHAR(64) COMMENT '数据哈希值，用于去重',
    status ENUM('draft', 'published', 'archived') DEFAULT 'published',
    source_type ENUM('manual', 'import', 'ai_extract') DEFAULT 'manual',
    source_info JSON COMMENT '数据来源信息',
    validation_status ENUM('pending', 'valid', 'invalid') DEFAULT 'pending',
    validation_errors JSON COMMENT '验证错误信息',
    created_by BIGINT NOT NULL,
    updated_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (updated_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_status (status),
    INDEX idx_source_type (source_type),
    INDEX idx_validation_status (validation_status),
    INDEX idx_record_hash (record_hash),
    INDEX idx_created_at (created_at)
);
```

#### import_jobs 表
```sql
CREATE TABLE import_jobs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    job_name VARCHAR(100),
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    total_rows INT DEFAULT 0,
    processed_rows INT DEFAULT 0,
    success_rows INT DEFAULT 0,
    error_rows INT DEFAULT 0,
    status ENUM('pending', 'processing', 'completed', 'failed', 'cancelled') DEFAULT 'pending',
    import_config JSON COMMENT '导入配置',
    error_details JSON COMMENT '错误详情',
    progress_info JSON COMMENT '进度信息',
    started_at TIMESTAMP NULL,
    completed_at TIMESTAMP NULL,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_status (status),
    INDEX idx_created_by (created_by),
    INDEX idx_created_at (created_at)
);
```

#### import_errors 表
```sql
CREATE TABLE import_errors (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    job_id BIGINT NOT NULL,
    row_number INT NOT NULL,
    field_name VARCHAR(100),
    error_type ENUM('validation', 'format', 'required', 'duplicate', 'reference') NOT NULL,
    error_message TEXT NOT NULL,
    original_value TEXT,
    suggested_value TEXT,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by BIGINT,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (job_id) REFERENCES import_jobs(id) ON DELETE CASCADE,
    FOREIGN KEY (resolved_by) REFERENCES users(id),
    INDEX idx_job_id (job_id),
    INDEX idx_error_type (error_type),
    INDEX idx_is_resolved (is_resolved)
);
```

#### data_templates 表
```sql
CREATE TABLE data_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    template_data JSON NOT NULL COMMENT '模板数据',
    description TEXT,
    usage_count INT DEFAULT 0,
    is_public BOOLEAN DEFAULT FALSE,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_is_public (is_public),
    INDEX idx_usage_count (usage_count)
);
```

#### ai_extraction_logs 表
```sql
CREATE TABLE ai_extraction_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    table_id BIGINT NOT NULL,
    source_content TEXT NOT NULL,
    extracted_data JSON NOT NULL,
    extraction_config JSON COMMENT '抽取配置',
    confidence_scores JSON COMMENT '置信度分数',
    processing_time INT COMMENT '处理时间(毫秒)',
    status ENUM('success', 'partial', 'failed') DEFAULT 'success',
    error_message TEXT,
    created_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (table_id) REFERENCES data_tables(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id),
    INDEX idx_table_id (table_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);
```

## API接口

### 数据录入接口

#### 创建数据记录
```
POST /api/tables/{tableId}/records
Authorization: Bearer {token}
Content-Type: application/json

{
  "recordData": {
    "field1": "value1",
    "field2": "value2"
  },
  "sourceType": "manual",
  "sourceInfo": {}
}
```

#### 获取数据记录列表
```
GET /api/tables/{tableId}/records
Authorization: Bearer {token}
Query Parameters:
  - page: int (默认0)
  - size: int (默认20)
  - search: string (可选)
  - status: string (可选)
  - sortBy: string (可选)
  - sortOrder: string (asc|desc)
```

#### 更新数据记录
```
PUT /api/tables/{tableId}/records/{recordId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "recordData": {
    "field1": "new_value1",
    "field2": "new_value2"
  }
}
```

#### 删除数据记录
```
DELETE /api/tables/{tableId}/records/{recordId}
Authorization: Bearer {token}
```

#### 批量操作
```
POST /api/tables/{tableId}/records/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "operation": "create|update|delete",
  "records": [
    {
      "id": 1, // update/delete时需要
      "recordData": {}
    }
  ]
}
```

### 导入导出接口

#### 上传导入文件
```
POST /api/tables/{tableId}/import/upload
Authorization: Bearer {token}
Content-Type: multipart/form-data

Form Data:
  - file: File (Excel或CSV文件)
  - jobName: string (可选)
  - config: string (JSON格式的导入配置)
```

#### 获取导入任务状态
```
GET /api/tables/{tableId}/import/jobs/{jobId}
Authorization: Bearer {token}
```

#### 获取导入任务列表
```
GET /api/tables/{tableId}/import/jobs
Authorization: Bearer {token}
Query Parameters:
  - page: int
  - size: int
  - status: string (可选)
```

#### 下载导入模板
```
GET /api/tables/{tableId}/import/template
Authorization: Bearer {token}
Query Parameters:
  - format: string (excel|csv)
  - includeExample: boolean (是否包含示例数据)
```

#### 下载导入错误报告
```
GET /api/tables/{tableId}/import/jobs/{jobId}/errors
Authorization: Bearer {token}
Query Parameters:
  - format: string (excel|csv|json)
```

#### 导出数据
```
GET /api/tables/{tableId}/export
Authorization: Bearer {token}
Query Parameters:
  - format: string (excel|csv|json)
  - filters: string (JSON格式的过滤条件)
  - fields: string (逗号分隔的字段列表)
```

### AI辅助录入接口

#### AI数据抽取
```
POST /api/tables/{tableId}/ai-extract
Authorization: Bearer {token}
Content-Type: application/json

{
  "sourceContent": "string", // 原始文本内容
  "extractionMode": "auto|guided", // 抽取模式
  "fieldHints": { // 字段提示
    "field1": "hint1",
    "field2": "hint2"
  }
}
```

#### 智能字段建议
```
POST /api/tables/{tableId}/ai-suggest
Authorization: Bearer {token}
Content-Type: application/json

{
  "partialData": {
    "field1": "value1"
  },
  "context": "string" // 上下文信息
}
```

#### 数据格式转换
```
POST /api/tables/{tableId}/ai-transform
Authorization: Bearer {token}
Content-Type: application/json

{
  "sourceData": {
    "field1": "raw_value1"
  },
  "targetFormat": {
    "field1": "target_format"
  }
}
```

### 数据验证接口

#### 验证单条记录
```
POST /api/tables/{tableId}/validate
Authorization: Bearer {token}
Content-Type: application/json

{
  "recordData": {
    "field1": "value1",
    "field2": "value2"
  }
}
```

#### 批量验证
```
POST /api/tables/{tableId}/validate/batch
Authorization: Bearer {token}
Content-Type: application/json

{
  "records": [
    {
      "recordData": {}
    }
  ]
}
```

## 数据验证引擎

### 验证规则定义

```java
@Component
public class DataValidationEngine {
    
    public ValidationResult validateRecord(
        DataTable table, 
        Map<String, Object> recordData) {
        
        ValidationResult result = new ValidationResult();
        
        for (TableField field : table.getFields()) {
            Object value = recordData.get(field.getFieldName());
            
            // 必填验证
            if (field.isRequired() && isEmpty(value)) {
                result.addError(field.getFieldName(), "字段不能为空");
                continue;
            }
            
            // 类型验证
            if (!validateFieldType(field, value)) {
                result.addError(field.getFieldName(), "字段类型不匹配");
                continue;
            }
            
            // 自定义验证规则
            validateCustomRules(field, value, result);
            
            // 唯一性验证
            if (field.isUnique() && !validateUniqueness(table, field, value)) {
                result.addError(field.getFieldName(), "字段值必须唯一");
            }
        }
        
        return result;
    }
    
    private boolean validateFieldType(TableField field, Object value) {
        if (value == null) return true;
        
        switch (field.getFieldType()) {
            case TEXT:
                return value instanceof String;
            case NUMBER:
                return value instanceof Number;
            case DATE:
                return isValidDate(value.toString());
            case BOOLEAN:
                return value instanceof Boolean;
            case EMAIL:
                return isValidEmail(value.toString());
            case PHONE:
                return isValidPhone(value.toString());
            default:
                return true;
        }
    }
    
    private void validateCustomRules(
        TableField field, 
        Object value, 
        ValidationResult result) {
        
        ValidationRules rules = field.getValidationRules();
        if (rules == null) return;
        
        // 长度验证
        if (rules.getMaxLength() != null && value.toString().length() > rules.getMaxLength()) {
            result.addError(field.getFieldName(), "字段长度超过限制");
        }
        
        // 范围验证
        if (rules.getMinValue() != null && value instanceof Number) {
            if (((Number) value).doubleValue() < rules.getMinValue()) {
                result.addError(field.getFieldName(), "字段值小于最小值");
            }
        }
        
        // 正则验证
        if (rules.getPattern() != null && !value.toString().matches(rules.getPattern())) {
            result.addError(field.getFieldName(), "字段格式不正确");
        }
    }
}
```

### 批量导入处理

```java
@Service
public class ImportService {
    
    @Async
    public void processImportJob(Long jobId) {
        ImportJob job = importJobRepository.findById(jobId).orElse(null);
        if (job == null) return;
        
        try {
            job.setStatus(ImportStatus.PROCESSING);
            job.setStartedAt(LocalDateTime.now());
            importJobRepository.save(job);
            
            List<Map<String, Object>> records = parseImportFile(job);
            job.setTotalRows(records.size());
            
            int successCount = 0;
            int errorCount = 0;
            
            for (int i = 0; i < records.size(); i++) {
                try {
                    Map<String, Object> record = records.get(i);
                    
                    // 数据验证
                    ValidationResult validation = validationEngine.validateRecord(
                        job.getTable(), record);
                    
                    if (validation.hasErrors()) {
                        saveImportErrors(job, i + 1, validation.getErrors());
                        errorCount++;
                    } else {
                        // 保存数据记录
                        saveDataRecord(job.getTable(), record, job.getCreatedBy());
                        successCount++;
                    }
                    
                    // 更新进度
                    job.setProcessedRows(i + 1);
                    job.setSuccessRows(successCount);
                    job.setErrorRows(errorCount);
                    
                    if (i % 100 == 0) {
                        importJobRepository.save(job);
                    }
                    
                } catch (Exception e) {
                    log.error("处理第{}行数据时出错", i + 1, e);
                    saveImportError(job, i + 1, "PROCESSING", e.getMessage());
                    errorCount++;
                }
            }
            
            job.setStatus(ImportStatus.COMPLETED);
            job.setCompletedAt(LocalDateTime.now());
            
        } catch (Exception e) {
            log.error("导入任务处理失败", e);
            job.setStatus(ImportStatus.FAILED);
            job.setErrorDetails(Map.of("error", e.getMessage()));
        } finally {
            importJobRepository.save(job);
        }
    }
    
    private List<Map<String, Object>> parseImportFile(ImportJob job) {
        String filePath = job.getFilePath();
        String fileName = job.getFileName();
        
        if (fileName.endsWith(".xlsx") || fileName.endsWith(".xls")) {
            return parseExcelFile(filePath);
        } else if (fileName.endsWith(".csv")) {
            return parseCsvFile(filePath);
        } else {
            throw new IllegalArgumentException("不支持的文件格式");
        }
    }
}
```

## AI数据抽取服务

```java
@Service
public class AIExtractionService {
    
    public ExtractionResult extractData(
        DataTable table, 
        String sourceContent, 
        ExtractionMode mode) {
        
        List<TableField> fields = table.getFields();
        Map<String, Object> extractedData = new HashMap<>();
        Map<String, Double> confidenceScores = new HashMap<>();
        
        for (TableField field : fields) {
            try {
                String prompt = buildExtractionPrompt(field, sourceContent, mode);
                AIResponse response = aiClient.generateCompletion(prompt);
                
                Object value = parseExtractedValue(field, response.getContent());
                Double confidence = calculateConfidence(response);
                
                extractedData.put(field.getFieldName(), value);
                confidenceScores.put(field.getFieldName(), confidence);
                
            } catch (Exception e) {
                log.warn("抽取字段{}时出错: {}", field.getFieldName(), e.getMessage());
                confidenceScores.put(field.getFieldName(), 0.0);
            }
        }
        
        return ExtractionResult.builder()
            .extractedData(extractedData)
            .confidenceScores(confidenceScores)
            .sourceContent(sourceContent)
            .build();
    }
    
    private String buildExtractionPrompt(
        TableField field, 
        String content, 
        ExtractionMode mode) {
        
        StringBuilder prompt = new StringBuilder();
        
        if (mode == ExtractionMode.AUTO) {
            prompt.append("从以下文本中抽取字段'")
                  .append(field.getDisplayName())
                  .append("'的值：\n\n");
        } else {
            prompt.append(field.getExtractionPrompt())
                  .append("\n\n文本内容：\n");
        }
        
        prompt.append(content)
              .append("\n\n请只返回抽取的值，不要包含其他说明。");
        
        return prompt.toString();
    }
    
    public SuggestionResult suggestFieldValues(
        DataTable table, 
        Map<String, Object> partialData, 
        String context) {
        
        Map<String, Object> suggestions = new HashMap<>();
        
        for (TableField field : table.getFields()) {
            if (partialData.containsKey(field.getFieldName())) {
                continue; // 已有值的字段跳过
            }
            
            String prompt = buildSuggestionPrompt(field, partialData, context);
            AIResponse response = aiClient.generateCompletion(prompt);
            
            Object suggestion = parseExtractedValue(field, response.getContent());
            if (suggestion != null) {
                suggestions.put(field.getFieldName(), suggestion);
            }
        }
        
        return SuggestionResult.builder()
            .suggestions(suggestions)
            .context(context)
            .build();
    }
}
```

## 前端界面

### 新增页面
- 数据录入页 (`/workspaces/{id}/tables/{tableId}/records/create`)
- 数据列表页 (`/workspaces/{id}/tables/{tableId}/records`)
- 数据编辑页 (`/workspaces/{id}/tables/{tableId}/records/{recordId}/edit`)
- 批量导入页 (`/workspaces/{id}/tables/{tableId}/import`)
- AI辅助录入页 (`/workspaces/{id}/tables/{tableId}/ai-entry`)

### 核心组件

#### DynamicForm - 动态表单组件
```vue
<template>
  <el-form 
    :model="formData" 
    :rules="formRules" 
    ref="dynamicForm"
    label-width="120px"
  >
    <el-form-item 
      v-for="field in fields" 
      :key="field.fieldName"
      :label="field.displayName"
      :prop="field.fieldName"
    >
      <component 
        :is="getFieldComponent(field.fieldType)"
        v-model="formData[field.fieldName]"
        :field="field"
        :options="field.fieldOptions"
        @change="handleFieldChange(field, $event)"
      />
      
      <div v-if="field.aiSuggestion" class="ai-suggestion">
        <el-tag size="small" type="info">
          AI建议: {{ field.aiSuggestion }}
        </el-tag>
        <el-button 
          size="small" 
          type="text"
          @click="acceptSuggestion(field)"
        >
          采用
        </el-button>
      </div>
    </el-form-item>
    
    <el-form-item>
      <el-button type="primary" @click="submitForm">保存</el-button>
      <el-button @click="resetForm">重置</el-button>
      <el-button 
        type="info" 
        @click="getAISuggestions"
        :loading="loadingSuggestions"
      >
        AI建议
      </el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: 'DynamicForm',
  props: {
    fields: Array,
    initialData: Object
  },
  data() {
    return {
      formData: {},
      formRules: {},
      loadingSuggestions: false
    }
  },
  methods: {
    getFieldComponent(fieldType) {
      const componentMap = {
        text: 'el-input',
        number: 'el-input-number',
        date: 'el-date-picker',
        boolean: 'el-switch',
        select: 'el-select',
        file: 'FileUpload'
      }
      return componentMap[fieldType] || 'el-input'
    },
    
    async getAISuggestions() {
      this.loadingSuggestions = true
      try {
        const response = await this.$api.post(
          `/tables/${this.tableId}/ai-suggest`,
          {
            partialData: this.formData,
            context: this.getFormContext()
          }
        )
        
        this.applySuggestions(response.data.suggestions)
      } catch (error) {
        this.$message.error('获取AI建议失败')
      } finally {
        this.loadingSuggestions = false
      }
    }
  }
}
</script>
```

#### ImportWizard - 导入向导组件
```vue
<template>
  <el-dialog v-model="visible" title="数据导入" width="800px">
    <el-steps :active="currentStep" align-center>
      <el-step title="上传文件" />
      <el-step title="字段映射" />
      <el-step title="数据预览" />
      <el-step title="导入结果" />
    </el-steps>
    
    <div class="step-content">
      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 0">
        <el-upload
          drag
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".xlsx,.xls,.csv"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 Excel (.xlsx, .xls) 和 CSV 文件
            </div>
          </template>
        </el-upload>
        
        <div v-if="uploadFile" class="file-info">
          <p>文件名: {{ uploadFile.name }}</p>
          <p>文件大小: {{ formatFileSize(uploadFile.size) }}</p>
        </div>
      </div>
      
      <!-- 步骤2: 字段映射 -->
      <div v-if="currentStep === 1">
        <FieldMapping 
          :file-headers="fileHeaders"
          :table-fields="tableFields"
          v-model="fieldMapping"
        />
      </div>
      
      <!-- 步骤3: 数据预览 -->
      <div v-if="currentStep === 2">
        <DataPreview 
          :preview-data="previewData"
          :field-mapping="fieldMapping"
          :validation-errors="validationErrors"
        />
      </div>
      
      <!-- 步骤4: 导入结果 -->
      <div v-if="currentStep === 3">
        <ImportResult 
          :job-id="importJobId"
          :import-status="importStatus"
        />
      </div>
    </div>
    
    <template #footer>
      <el-button @click="visible = false">取消</el-button>
      <el-button 
        v-if="currentStep > 0" 
        @click="prevStep"
      >
        上一步
      </el-button>
      <el-button 
        v-if="currentStep < 3" 
        type="primary" 
        @click="nextStep"
        :disabled="!canProceed"
      >
        下一步
      </el-button>
      <el-button 
        v-if="currentStep === 2" 
        type="success" 
        @click="startImport"
        :loading="importing"
      >
        开始导入
      </el-button>
    </template>
  </el-dialog>
</template>
```

#### AIExtractionPanel - AI抽取面板
```vue
<template>
  <div class="ai-extraction-panel">
    <el-card header="AI数据抽取">
      <el-form :model="extractionForm">
        <el-form-item label="源文本">
          <el-input 
            v-model="extractionForm.sourceContent"
            type="textarea"
            :rows="6"
            placeholder="请输入要抽取数据的文本内容"
          />
        </el-form-item>
        
        <el-form-item label="抽取模式">
          <el-radio-group v-model="extractionForm.mode">
            <el-radio label="auto">自动抽取</el-radio>
            <el-radio label="guided">引导抽取</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="extractData"
            :loading="extracting"
          >
            开始抽取
          </el-button>
          <el-button @click="clearResults">清空结果</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    
    <el-card header="抽取结果" v-if="extractionResult">
      <div class="extraction-results">
        <div 
          v-for="(value, field) in extractionResult.extractedData" 
          :key="field"
          class="result-item"
        >
          <div class="field-info">
            <span class="field-name">{{ getFieldDisplayName(field) }}</span>
            <el-tag 
              :type="getConfidenceType(extractionResult.confidenceScores[field])"
              size="small"
            >
              置信度: {{ (extractionResult.confidenceScores[field] * 100).toFixed(1) }}%
            </el-tag>
          </div>
          <div class="field-value">
            <el-input 
              v-model="extractionResult.extractedData[field]"
              @change="updateExtractedValue(field, $event)"
            />
          </div>
        </div>
      </div>
      
      <div class="result-actions">
        <el-button type="success" @click="acceptAllResults">
          接受所有结果
        </el-button>
        <el-button @click="saveAsTemplate">
          保存为模板
        </el-button>
      </div>
    </el-card>
  </div>
</template>
```

## 测试用例

### 功能测试
- 数据录入和编辑功能测试
- 批量导入功能测试
- AI辅助录入功能测试
- 数据验证功能测试
- 导出功能测试

### 性能测试
- 大批量数据导入性能测试
- 并发录入性能测试
- AI抽取响应时间测试
- 数据验证性能测试

### 兼容性测试
- 不同格式文件导入测试
- 不同浏览器兼容性测试
- 移动端录入体验测试

## 验收标准

- [ ] 用户可以手动录入和编辑数据
- [ ] 支持Excel/CSV文件批量导入
- [ ] AI辅助录入功能正常工作
- [ ] 数据验证规则正确执行
- [ ] 导入错误处理机制完善
- [ ] 数据导出功能完整
- [ ] 批量操作性能满足要求
- [ ] 所有API接口测试通过
- [ ] 前端界面用户体验良好
- [ ] AI服务集成稳定可靠